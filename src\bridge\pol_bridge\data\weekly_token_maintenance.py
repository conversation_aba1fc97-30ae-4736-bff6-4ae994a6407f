#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
每周代币维护脚本
启动时执行一次，然后每7天循环执行

执行任务：
1. 代币路由测试
2. 筛选零地址交易代币
3. 清理黑名单代币
"""

import subprocess
import sys
import os
import time
from datetime import datetime, timedelta

def run_command(command: str, description: str):
    """执行命令"""
    print(f"\n[{description}]")
    print(f"执行命令: {command}")
    print("-" * 50)

    try:
        # 直接运行命令，让输出直接显示
        result = subprocess.run(command, shell=True)

        if result.returncode == 0:
            print(f"\n>>> {description} 执行成功")
        else:
            print(f"\n>>> {description} 执行失败 (返回码: {result.returncode})")

    except Exception as e:
        print(f"\n>>> {description} 执行异常: {str(e)}")

def execute_maintenance_tasks():
    """执行维护任务"""
    print("=" * 60)
    print(f"开始执行代币维护任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 按顺序执行三个命令
    commands = [
        ("python src/bridge/pol_bridge/data/test_tokens_routing.py --workers 4 --amount 0.1", "1/3 代币路由测试"),
        ("python src/bridge/pol_bridge/data/filter_poly_tokens_zero_tx.py --threads 1", "2/3 筛选零地址交易代币"),
        ("python scripts/arbitrage/portal_polygon_bridge/clean_blacklisted_tokens.py", "3/3 清理黑名单代币")
    ]

    for command, description in commands:
        run_command(command, description)
        print("\n" + "=" * 60)

    print(f"所有任务执行完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("每周代币维护脚本启动")
    print("=" * 60)

    while True:
        try:
            # 执行维护任务
            execute_maintenance_tasks()

            # 计算下次执行时间（7天后）
            next_run = datetime.now() + timedelta(days=7)
            print(f"\n下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            print("等待7天后执行下一次维护...")
            print("按 Ctrl+C 可以停止脚本")
            print("=" * 60)

            # 等待7天（7 * 24 * 60 * 60 = 604800秒）
            time.sleep(604800)

        except KeyboardInterrupt:
            print("\n\n脚本被用户中断")
            print("=" * 60)
            break
        except Exception as e:
            print(f"\n脚本执行出错: {str(e)}")
            print("等待1小时后重试...")
            time.sleep(3600)  # 等待1小时后重试

if __name__ == "__main__":
    main()
