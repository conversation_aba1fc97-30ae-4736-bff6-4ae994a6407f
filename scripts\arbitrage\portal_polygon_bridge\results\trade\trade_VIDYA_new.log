2025-07-26 15:08:03,520 - trade_VIDYA - INFO - ================================================================================
2025-07-26 15:08:03,520 - trade_VIDYA - INFO - 开始执行 VIDYA 买入交易 - 时间: 2025-07-26 15:08:03
2025-07-26 15:08:03,520 - trade_VIDYA - INFO - 链: polygon, 投入金额: 16.83 USDT
2025-07-26 15:08:03,520 - trade_VIDYA - INFO - 代币地址: 0xfe9ca7cf13e33b23af63fea696f4aae1b7a65585
2025-07-26 15:08:03,526 - trade_VIDYA - INFO - VIDYA: 将在polygon链上执行买入，代币地址: 0xfe9ca7cf13e33b23af63fea696f4aae1b7a65585
2025-07-26 15:08:03,526 - trade_VIDYA - INFO - VIDYA: 准备使用KyberSwap在polygon上执行16.83USDT买入VIDYA交易
2025-07-26 15:08:03,527 - trade_VIDYA - INFO - 执行买入交易，预期代币输出: 851.5301831449185
2025-07-26 15:08:14,661 - trade_VIDYA - INFO - VIDYA: 使用tx_token_change_tracker获取交易 0x6b2c4917f49d4a1636c10851c9a3443f869c9422a9426acbd70c7b42179771e7 的代币数量...
2025-07-26 15:08:21,587 - trade_VIDYA - INFO - VIDYA: 从tx_token_change_tracker成功获取到代币数量: 851.506733312961770884
2025-07-26 15:08:21,587 - trade_VIDYA - INFO - 交易执行成功:
2025-07-26 15:08:21,587 - trade_VIDYA - INFO -   交易哈希: 0x6b2c4917f49d4a1636c10851c9a3443f869c9422a9426acbd70c7b42179771e7
2025-07-26 15:08:21,587 - trade_VIDYA - INFO -   实际输出数量: 851.5067333129617 VIDYA
2025-07-26 15:08:21,587 - trade_VIDYA - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-26 15:08:21,587 - trade_VIDYA - INFO - ================================================================================
2025-07-26 15:08:21,588 - trade_VIDYA - INFO - 开始执行 VIDYA 桥接操作 - 时间: 2025-07-26 15:08:21
2025-07-26 15:08:21,588 - trade_VIDYA - INFO - 桥接方向: polygon_to_ethereum
2025-07-26 15:08:21,588 - trade_VIDYA - INFO - 代币数量: 851.506733312961770884 VIDYA
2025-07-26 15:08:23,432 - trade_VIDYA - INFO - 从Polygon桥接到以太坊: 851.506733312961770884 VIDYA
2025-07-26 15:50:25,450 - trade_VIDYA - INFO - 桥接操作成功完成
2025-07-26 15:50:25,450 - trade_VIDYA - INFO - Burn交易哈希: 0x4cf7cf8c75cf222f5d0713412e1b9514dcd4f059240570ab6a41fe11fb42d2b8
2025-07-26 15:50:25,450 - trade_VIDYA - INFO - Claim交易哈希: 0x59c6177dbc50b3260ec589712f5c392b7e1c901b65d912077f3de44612d3ca75
2025-07-26 15:50:25,451 - trade_VIDYA - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\VIDYA_20250726_155025_bridge.json
2025-07-26 15:50:25,451 - trade_VIDYA - INFO - 桥接操作成功完成
2025-07-26 15:50:25,451 - trade_VIDYA - INFO - 开始执行卖出操作...
2025-07-26 15:50:25,451 - trade_VIDYA - INFO - ================================================================================
2025-07-26 15:50:25,451 - trade_VIDYA - INFO - 开始执行 VIDYA 卖出交易 - 时间: 2025-07-26 15:50:25
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 目标链: ethereum
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 目标链代币地址: ******************************************
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 在ethereum链上执行卖出操作
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 代币地址: ******************************************
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 卖出数量: 851.506733312961770884 VIDYA
2025-07-26 15:50:25,458 - trade_VIDYA - INFO - 预期USDT输出: 20.510305 USDT
2025-07-26 15:50:28,878 - trade_VIDYA - INFO - 执行卖出交易，预期USDT输出: 20.510305
2025-07-26 15:51:25,776 - trade_VIDYA - INFO - 使用tx_token_change_tracker获取交易 0xe55343dc93a6fded98d46aea60d7657da144282dbcc95c2e4424a8439606a62b 的USDT数量...
2025-07-26 15:51:27,748 - trade_VIDYA - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-26 15:51:27,748 - trade_VIDYA - INFO - 成功获取到USDT数量: 20.5182
2025-07-26 15:51:27,748 - trade_VIDYA - INFO - 卖出交易执行成功:
2025-07-26 15:51:27,748 - trade_VIDYA - INFO -   交易哈希: 0xe55343dc93a6fded98d46aea60d7657da144282dbcc95c2e4424a8439606a62b
2025-07-26 15:51:27,748 - trade_VIDYA - INFO -   实际收到USDT数量: 20.5182
2025-07-26 15:51:27,749 - trade_VIDYA - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\VIDYA_20250726_155127_sell.json
2025-07-26 15:51:27,749 - trade_VIDYA - INFO - 卖出操作成功完成
2025-07-26 15:51:27,749 - trade_VIDYA - INFO - 交易执行完成，耗时: 2604.23秒
2025-07-26 15:51:27,749 - trade_VIDYA - INFO - ================================================================================
