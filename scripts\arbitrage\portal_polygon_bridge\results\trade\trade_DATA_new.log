2025-07-28 14:01:30,363 - trade_DATA - INFO - ================================================================================
2025-07-28 14:01:30,364 - trade_DATA - INFO - 开始执行 DATA 买入交易 - 时间: 2025-07-28 14:01:30
2025-07-28 14:01:30,364 - trade_DATA - INFO - 链: ethereum, 投入金额: 19.2 USDT
2025-07-28 14:01:30,364 - trade_DATA - INFO - 代币地址: ******************************************
2025-07-28 14:01:30,372 - trade_DATA - INFO - DATA: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-28 14:01:30,372 - trade_DATA - INFO - DATA: 准备使用KyberSwap在ethereum上执行19.2USDT买入DATA交易
2025-07-28 14:01:30,373 - trade_DATA - INFO - 执行买入交易，预期代币输出: 1.1285638550641588
2025-07-28 14:02:12,695 - trade_DATA - INFO - DATA: 使用tx_token_change_tracker获取交易 0x9bb7a9dd99aece3f3c898e73ca992cd1ea71ab9632d57aacd88c25c425566b63 的代币数量...
2025-07-28 14:02:16,586 - trade_DATA - INFO - DATA: 从tx_token_change_tracker成功获取到代币数量: 1.128090607096289216
2025-07-28 14:02:16,586 - trade_DATA - INFO - 交易执行成功:
2025-07-28 14:02:16,586 - trade_DATA - INFO -   交易哈希: 0x9bb7a9dd99aece3f3c898e73ca992cd1ea71ab9632d57aacd88c25c425566b63
2025-07-28 14:02:16,586 - trade_DATA - INFO -   实际输出数量: 1.1280906070962893 DATA
2025-07-28 14:02:16,586 - trade_DATA - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-28 14:02:16,586 - trade_DATA - INFO - ================================================================================
2025-07-28 14:02:16,586 - trade_DATA - INFO - 开始执行 DATA 桥接操作 - 时间: 2025-07-28 14:02:16
2025-07-28 14:02:16,586 - trade_DATA - INFO - 桥接方向: ethereum_to_polygon
2025-07-28 14:02:16,586 - trade_DATA - INFO - 代币数量: 1.128090607096289216 DATA
2025-07-28 14:02:16,586 - trade_DATA - INFO - 尝试初始化桥接 (第 1 次)
2025-07-28 14:02:28,881 - trade_DATA - INFO - 桥接初始化成功
2025-07-28 14:02:28,882 - trade_DATA - INFO - 从以太坊桥接到Polygon: 1.128090607096289216 DATA
2025-07-28 14:24:37,067 - trade_DATA - INFO - 桥接操作成功完成
2025-07-28 14:24:37,069 - trade_DATA - INFO - 桥接交易哈希: 0x13a77bb62172af71aab646d5d83fb356d1762a576e8ab67f12f3b34a6e669b88
2025-07-28 14:24:37,069 - trade_DATA - INFO - Polygon交易哈希: None
2025-07-28 14:24:37,071 - trade_DATA - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\DATA_20250728_142437_bridge.json
2025-07-28 14:24:37,072 - trade_DATA - INFO - 桥接操作成功完成
2025-07-28 14:24:37,072 - trade_DATA - INFO - 开始执行卖出操作...
2025-07-28 14:24:37,072 - trade_DATA - INFO - ================================================================================
2025-07-28 14:24:37,072 - trade_DATA - INFO - 开始执行 DATA 卖出交易 - 时间: 2025-07-28 14:24:37
2025-07-28 14:24:37,082 - trade_DATA - INFO - 目标链: polygon
2025-07-28 14:24:37,082 - trade_DATA - INFO - 目标链代币地址: 0x1d607faa0a51518a7728580c238d912747e71f7a
2025-07-28 14:24:37,082 - trade_DATA - INFO - 在polygon链上执行卖出操作
2025-07-28 14:24:37,082 - trade_DATA - INFO - 代币地址: 0x1d607faa0a51518a7728580c238d912747e71f7a
2025-07-28 14:24:37,082 - trade_DATA - INFO - 卖出数量: 1.128090607096289216 DATA
2025-07-28 14:24:37,082 - trade_DATA - INFO - 预期USDT输出: 20.949008 USDT
2025-07-28 14:24:45,480 - trade_DATA - INFO - 执行卖出交易，预期USDT输出: 20.949008
2025-07-28 14:25:05,280 - trade_DATA - INFO - 使用tx_token_change_tracker获取交易 0x1ddd3b3d97eedf855102d3c68fd0da1e5033c85141de0976b0513b782892ba8a 的USDT数量...
2025-07-28 14:25:19,299 - trade_DATA - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-28 14:25:19,299 - trade_DATA - INFO - 成功获取到USDT数量: 20.914026
2025-07-28 14:25:19,299 - trade_DATA - INFO - 卖出交易执行成功:
2025-07-28 14:25:19,299 - trade_DATA - INFO -   交易哈希: 0x1ddd3b3d97eedf855102d3c68fd0da1e5033c85141de0976b0513b782892ba8a
2025-07-28 14:25:19,299 - trade_DATA - INFO -   实际收到USDT数量: 20.914026
2025-07-28 14:25:19,300 - trade_DATA - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\DATA_20250728_142519_sell.json
2025-07-28 14:25:19,300 - trade_DATA - INFO - 卖出操作成功完成
2025-07-28 14:25:19,300 - trade_DATA - INFO - 交易执行完成，耗时: 1428.94秒
2025-07-28 14:25:19,300 - trade_DATA - INFO - ================================================================================
