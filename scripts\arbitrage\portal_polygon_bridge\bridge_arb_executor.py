#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Polygon和以太坊网络间套利执行器
执行验证过的套利机会
"""

import os
import sys
import json
import time
import logging
import asyncio
import threading
import argparse
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime
import queue
import traceback
from web3 import Web3
from decimal import Decimal
from src.network.tx_token_change_tracker import get_last_received_token
import yaml

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

# 导入KyberSwapClient
from src.dex.KyberSwap.client import KyberSwapClient
from src.dex.KyberSwap.swap import swap_tokens
from src.utils.decimal.token_decimal import get_token_decimals, token_decimal_helper

# 导入桥接模块
from src.bridge.pol_bridge.auto_bridge import AutoBridge

def init_directories():
    """初始化必要的目录结构"""
    base_dir = os.path.dirname(__file__)
    directories = [
        os.path.join(base_dir, "results"),
        os.path.join(base_dir, "results", "trade"),
        os.path.join(base_dir, "results", "profit"),
        os.path.join(base_dir, "results", "logs")
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def manage_log_file(log_file_path):
    """管理日志文件，如果文件过大则进行轮转"""
    if os.path.exists(log_file_path):
        file_size = os.path.getsize(log_file_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            backup_path = f"{log_file_path}.{int(time.time())}"
            os.rename(log_file_path, backup_path)

def get_symbol_log_file(symbol):
    """获取代币的日志文件路径"""
    log_dir = os.path.join(os.path.dirname(__file__), "results", "logs")
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, f"{symbol}.log")

def get_trade_log_file(symbol):
    """获取交易日志文件路径"""
    log_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, f"trade_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

def load_config() -> Dict:
    """加载配置文件"""
    # 确定项目根目录，并加载配置文件
    current_dir = os.path.dirname(__file__)
    
    # 向上查找三级，直到找到config目录
    for _ in range(5):
        config_path = os.path.join(current_dir, "config", "config.yaml")
        if os.path.exists(config_path):
            break
        current_dir = os.path.dirname(current_dir)
    
    # 如果仍然找不到，尝试从工作目录找
    if not os.path.exists(config_path):
        project_root = os.getcwd()
        while project_root and not os.path.exists(os.path.join(project_root, "config", "config.yaml")):
            project_root = os.path.dirname(project_root)
        
        config_path = os.path.join(project_root, "config", "config.yaml")
        
    # 如果仍然找不到，则使用绝对路径
    if not os.path.exists(config_path):
        config_path = "C:/Users/<USER>/CascadeProjects/cex_dex_arb_dev/config/config.yaml"
    
    # 如果仍然找不到，返回空字典
    if not os.path.exists(config_path):
        logging.error(f"找不到配置文件: {config_path}")
        return {}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return {}

def save_profit_record(symbol: str, usdt_input: float, usdt_received: float, eth_address: str = "", polygon_address: str = "") -> None:
    """保存利润记录"""
    profit_dir = os.path.join(os.path.dirname(__file__), "results", "profit")
    os.makedirs(profit_dir, exist_ok=True)
    
    record = {
        "symbol": symbol,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "usdt_input": usdt_input,
        "usdt_received": usdt_received,
        "profit": usdt_received - usdt_input,
        "profit_percentage": ((usdt_received - usdt_input) / usdt_input * 100) if usdt_input > 0 else 0,
        "eth_address": eth_address,
        "polygon_address": polygon_address
    }
    
    # 生成文件名，添加_new后缀
    file_path = os.path.join(profit_dir, f"profit_{symbol}_{datetime.now().strftime('%Y%m%d')}_new.json")
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                records = json.load(f)
        else:
            records = []
            
        records.append(record)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(records, f, indent=4, ensure_ascii=False)
            
    except Exception as e:
        logging.error(f"保存利润记录时出错: {str(e)}")
        logging.error(traceback.format_exc())

# 设置日志格式
def setup_logger(symbol: str) -> logging.Logger:
    """设置日志记录器"""
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建固定的日志文件名，添加_new后缀
    log_file = os.path.join(log_dir, f"trade_{symbol}_new.log")
    
    # 管理日志文件大小
    manage_log_file(log_file)
    
    # 创建日志记录器
    logger = logging.getLogger(f"trade_{symbol}")
    logger.setLevel(logging.INFO)
    
    # 如果已经有处理器，先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class BridgeArbExecutor:
    def __init__(self, symbol: str, chain: str, token_address: str, amount: float, bridge_direction: str = None, token_data: Dict = None, expected_usdt_out: float = None, expected_token_out: float = None):
        """
        初始化执行器

        Args:
            symbol: 代币符号
            chain: 链名称 (ethereum 或 polygon)
            token_address: 代币地址
            amount: USDT数量
            bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)，可选
            token_data: 代币数据，包含 polygon_address 和 eth_address
            expected_usdt_out: 预期卖出获取的USDT数量 (USDTOLD)
            expected_token_out: 预期买入获取的代币数量
        """
        self.symbol = symbol
        self.chain = chain.lower()
        self.token_address = token_address
        self.amount = amount
        self.bridge_direction = bridge_direction
        self.token_data = token_data or {}
        self.expected_usdt_out = expected_usdt_out  # 预期的USDT输出量
        self.expected_token_out = expected_token_out  # 预期的代币输出量
        
        # 初始化目录
        init_directories()
        
        # 设置日志记录器
        self.logger = setup_logger(symbol)

        # 加载配置文件
        self.config = self.load_config()

        # 创建KyberSwap客户端
        self.client = KyberSwapClient(chain=self.chain)
        
        # 设置USDT地址
        self.eth_usdt = "******************************************"  # 以太坊USDT
        self.polygon_usdt = "******************************************"  # Polygon USDT
        
        # 获取正确的USDT地址
        self.usdt_address = self.eth_usdt if self.chain == "ethereum" else self.polygon_usdt
        
        # 初始化结果队列
        self.result_queue = queue.Queue()
        
        # 验证链和交易方向是否匹配
        if self.bridge_direction:
            expected_chain = "ethereum" if self.bridge_direction == "ethereum_to_polygon" else "polygon"
            if self.chain != expected_chain:
                self.logger.warning(f"警告: 指定的链 {self.chain} 与交易方向 {self.bridge_direction} 不匹配")
                self.logger.warning(f"根据交易方向，应该在 {expected_chain} 链上执行买入操作")
                # 自动修正链
                self.chain = expected_chain
                self.logger.info(f"已自动修正为在 {self.chain} 链上执行操作")
                # 重新创建客户端和设置USDT地址
                self.client = KyberSwapClient(chain=self.chain)
                self.usdt_address = self.eth_usdt if self.chain == "ethereum" else self.polygon_usdt

    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            import yaml
            config_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "config", "config.yaml")
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            return {}

    @classmethod
    def from_opportunity(cls, opportunity: Dict) -> 'BridgeArbExecutor':
        """
        从套利机会信息创建执行器实例

        Args:
            opportunity: 套利机会信息，包含以下字段：
                - symbol: 代币符号
                - polygon_address: Polygon链上的代币地址
                - eth_address: 以太坊链上的代币地址
                - bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)
                - usdt_input: USDT数量
                - token_amount: 代币数量
                - expected_usdt_out: 预期卖出获取的USDT数量 (USDTOLD)
                - expected_token_out: 预期买入获取的代币数量

        Returns:
            BridgeArbExecutor: 执行器实例
        """
        symbol = opportunity.get('symbol', 'Unknown')
        bridge_direction = opportunity.get('bridge_direction', '')
        # 从opportunity中获取usdt_input
        usdt_input = opportunity.get('usdt_input', 0)
        # 从opportunity中获取预期的USDT输出量
        expected_usdt_out = opportunity.get('expected_usdt_out', opportunity.get('usdt_output', 0))
        # 从opportunity中获取预期的代币输出量
        expected_token_out = opportunity.get('expected_token_out', opportunity.get('token_amount', 0))

        # 根据bridge_direction确定正确的链和token_address
        if bridge_direction == 'ethereum_to_polygon':
            chain = 'ethereum'
            token_address = opportunity.get('eth_address', '')
        elif bridge_direction == 'polygon_to_ethereum':
            chain = 'polygon'
            token_address = opportunity.get('polygon_address', '')
        else:
            # 如果没有bridge_direction，使用lower_chain
            chain = opportunity.get('lower_chain', '')
            if chain == 'ethereum':
                token_address = opportunity.get('eth_address', '')
            else:  # polygon
                token_address = opportunity.get('polygon_address', '')

        # 验证必要参数
        if not all([symbol, chain, token_address, usdt_input > 0]):
            raise ValueError(f"缺少必要的交易信息或金额无效: symbol={symbol}, chain={chain}, token_address={token_address}, usdt_input={usdt_input}")

        # 创建token_data字典
        token_data = {
            'symbol': symbol,
            'polygon_address': opportunity.get('polygon_address', ''),
            'eth_address': opportunity.get('eth_address', '')
        }

        return cls(
            symbol=symbol,
            chain=chain,
            token_address=token_address,
            amount=usdt_input,
            bridge_direction=bridge_direction,
            token_data=token_data,
            expected_usdt_out=expected_usdt_out,
            expected_token_out=expected_token_out
        )

    async def execute_buy(self) -> Dict:
        """
        执行买入操作
        
        Returns:
            Dict: 买入结果
        """
        start_time = time.time()
        
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 买入交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"链: {self.chain}, 投入金额: {self.amount} USDT")
            self.logger.info(f"代币地址: {self.token_address}")
            
            # 加载完整的代币地址信息
            token_addresses = load_token_addresses(self.symbol)

            # 创建执行记录
            execution_record = {
                'symbol': self.symbol,
                'chain': self.chain,
                'buy_chain': self.chain.lower(),
                'eth_address': token_addresses.get('eth_address', '') or (self.token_address if self.chain == "ethereum" else ""),
                'polygon_address': token_addresses.get('polygon_address', '') or (self.token_address if self.chain == "polygon" else ""),
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'usdt_input': self.amount,
                'success': False,
                'amount_bought': 0,
                'tx_hash': '',
                'price': 0,
                'error': None
            }
            
            # 检查USDT输入金额是否有效
            if self.amount <= 0:
                error_msg = f"USDT输入金额必须大于0: {self.amount}"
                self.logger.error(f"{self.symbol}: {error_msg}")
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            # 验证代币地址
            if not self.token_address or not Web3.is_address(self.token_address):
                error_msg = f"无效的代币地址: {self.token_address}"
                self.logger.error(f"{self.symbol}: {error_msg}")
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            self.logger.info(f"{self.symbol}: 将在{self.chain}链上执行买入，代币地址: {self.token_address}")
            self.logger.info(f"{self.symbol}: 准备使用KyberSwap在{self.chain}上执行{self.amount}USDT买入{self.symbol}交易")
            
            # 调用swap_tokens执行交易（包含预期代币数量验证）
            if self.expected_token_out and self.expected_token_out > 0:
                self.logger.info(f"执行买入交易，预期代币输出: {self.expected_token_out}")
            else:
                self.logger.info("执行买入交易（无预期输出验证）")

            swap_result = await swap_tokens(
                chain=self.chain,
                token_in="USDT",  # 使用简化的token_in
                token_out=self.token_address,
                amount=self.amount,  # 直接使用原始金额
                slippage=0.5,  # 0.5%滑点
                timeout=1200,  # 20分钟超时
                gas_multiplier=1.8,  # 增加gas限制
                save_gas=True,
                real=True,  # 执行实际交易
                expected_token_out=self.expected_token_out  # 传递预期代币输出用于预验证
            )
            
            if not swap_result.get("success"):
                error_msg = swap_result.get("error", "未知错误")
                self.logger.error(f"交易执行失败: {error_msg}")
                execution_record['error'] = f"交易执行失败: {error_msg}"
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            # 获取交易结果
            tx_hash = swap_result.get("tx_hash")
            
            # 获取实际买入的代币数量
            actual_amount = 0
            
            try:
                # 获取钱包地址
                config = load_config()
                if not config:
                    self.logger.error("无法加载配置文件")
                    return {
                        "success": False,
                        "error": "无法加载配置文件",
                        "execution_record": execution_record
                    }
                    
                private_key = config.get("dex", {}).get(self.chain, {}).get("wallet", {}).get("private_key", "")
                if not private_key:
                    self.logger.error(f"无法获取{self.chain}链的私钥")
                    return {
                        "success": False,
                        "error": f"无法获取{self.chain}链的私钥",
                        "execution_record": execution_record
                    }
                    
                # 使用 client 的 web3 实例
                web3 = self.client.web3
                account = web3.eth.account.from_key(private_key)
                user_address = account.address
                
                # 添加重试逻辑获取代币数量
                max_retries = 5
                last_error = None
                token_result = None

                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            self.logger.info(f"{self.symbol}: 第{attempt + 1}次尝试获取代币数量...")

                        # 使用tx_token_change_tracker获取代币数量
                        self.logger.info(f"{self.symbol}: 使用tx_token_change_tracker获取交易 {tx_hash} 的代币数量...")

                        token_result = get_last_received_token(
                            tx_hash=tx_hash,
                            chain=self.chain,
                            user_address=user_address
                        )

                        if "error" not in token_result:
                            # 保持原始字符串格式的数量，避免精度损失
                            actual_amount_str = str(token_result["amount"])
                            actual_amount = float(actual_amount_str)  # 仅用于显示和计算价格
                            self.logger.info(f"{self.symbol}: 从tx_token_change_tracker成功获取到代币数量: {actual_amount_str}")
                            break
                        else:
                            last_error = token_result["error"]
                            self.logger.warning(f"{self.symbol}: 第{attempt + 1}次获取代币数量失败: {last_error}")
                            if attempt < max_retries - 1:
                                # 线性递增重试时间：10-120秒
                                retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                                self.logger.info(f"{self.symbol}: 等待{retry_delay}秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue

                    except Exception as e:
                        last_error = str(e)
                        self.logger.warning(f"{self.symbol}: 第{attempt + 1}次获取代币数量出错: {last_error}")
                        if attempt < max_retries - 1:
                            # 线性递增重试时间：10-120秒
                            retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                            self.logger.info(f"{self.symbol}: 等待{retry_delay}秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue
                
                if "error" in token_result:
                    self.logger.error(f"{self.symbol}: 在{max_retries}次尝试后仍然无法获取代币数量: {last_error}")
                    execution_record['error'] = f"无法获取代币数量: {last_error}"
                    return {
                        "success": False,
                        "error": f"无法获取代币数量: {last_error}",
                        "execution_record": execution_record
                    }
                
                # 更新执行记录
                execution_record.update({
                    'success': True,
                    'amount_bought': actual_amount_str,  # 使用字符串格式保存
                    'tx_hash': tx_hash,
                    'price': float(self.amount) / float(actual_amount_str) if float(actual_amount_str) > 0 else 0,
                    'token_symbol': token_result.get('symbol', ''),
                    'token_name': token_result.get('name', ''),
                    'token_decimals': token_result.get('decimals', 18)
                })
                
                self.logger.info("交易执行成功:")
                self.logger.info(f"  交易哈希: {tx_hash}")
                self.logger.info(f"  实际输出数量: {actual_amount} {self.symbol}")
                
                # 如果指定了桥接方向，执行桥接操作
                if self.bridge_direction:
                    self.logger.info(f"开始执行桥接操作，方向: {self.bridge_direction}")
                    bridge_result = await self.bridge_tokens(actual_amount_str)
                    
                    if not bridge_result.get("success"):
                        error_msg = bridge_result.get("error", "未知错误")
                        self.logger.error(f"桥接操作失败: {error_msg}")

                        # 启动智能恢复机制
                        self.logger.info("启动桥接失败智能恢复机制...")
                        recovery_result = await self.handle_bridge_failure_recovery(actual_amount_str)

                        if recovery_result.get("success"):
                            self.logger.info("桥接恢复成功")
                            execution_record['bridge_success'] = True
                            execution_record['bridge_recovery'] = True
                            execution_record.update(recovery_result.get('bridge_record', {}))
                        else:
                            self.logger.error(f"桥接恢复失败: {recovery_result.get('error', '未知错误')}")
                            execution_record['bridge_error'] = error_msg
                            execution_record['recovery_error'] = recovery_result.get('error', '未知错误')
                    else:
                        self.logger.info("桥接操作成功完成")
                        execution_record['bridge_success'] = True
                        if self.bridge_direction == "ethereum_to_polygon":
                            execution_record['bridge_tx'] = bridge_result['bridge_record'].get('bridge_tx')
                            execution_record['polygon_tx'] = bridge_result['bridge_record'].get('polygon_tx')
                        else:
                            execution_record['burn_tx'] = bridge_result['bridge_record'].get('burn_tx_hash')
                            execution_record['claim_tx'] = bridge_result['bridge_record'].get('claim_tx_hash')
                        
                        # 桥接成功后执行卖出操作
                        self.logger.info("开始执行卖出操作...")

                        # 添加重试逻辑处理"未知"状态
                        max_sell_retries = 3
                        sell_success = False
                        sell_result = None

                        for sell_attempt in range(max_sell_retries):
                            try:
                                if sell_attempt > 0:
                                    self.logger.info(f"第{sell_attempt + 1}次尝试卖出操作...")

                                # 使用原来的方式执行卖出（包含预期输出验证）
                                sell_result = await self.execute_sell(actual_amount_str)

                                if sell_result.get("success"):
                                    self.logger.info("卖出操作成功完成")
                                    execution_record['sell_success'] = True
                                    execution_record['sell_tx'] = sell_result.get('tx_hash')
                                    execution_record['usdt_received'] = sell_result.get('usdt_received')
                                    sell_success = True
                                    break
                                else:
                                    error_msg = sell_result.get("error", "未知错误")
                                    status = sell_result.get("status", "失败")

                                    # 检查是否需要重试（仅对"未知"状态重试）
                                    if sell_result.get("need_retry") and status == "未知":
                                        self.logger.warning(f"第{sell_attempt + 1}次卖出失败，状态: {status}, 错误: {error_msg}")

                                        if sell_attempt < max_sell_retries - 1:
                                            # 线性递增重试时间：20-88秒
                                            retry_delay = 20 + (sell_attempt * 34)  # 20, 54, 88
                                            self.logger.info(f"等待{retry_delay}秒后重试卖出...")
                                            await asyncio.sleep(retry_delay)
                                            continue
                                        else:
                                            self.logger.error(f"卖出操作在{max_sell_retries}次尝试后仍然失败: {error_msg}")
                                            execution_record['sell_error'] = error_msg
                                            break
                                    else:
                                        # 非"未知"状态的错误，不重试
                                        self.logger.error(f"卖出操作失败: {error_msg}")
                                        execution_record['sell_error'] = error_msg
                                        break

                            except Exception as e:
                                error_msg = f"卖出操作异常: {str(e)}"
                                self.logger.error(error_msg)
                                self.logger.error(traceback.format_exc())
                                execution_record['sell_error'] = error_msg
                                break

                        if not sell_success:
                            final_error = sell_result.get("error", "未知错误") if sell_result else "卖出操作失败"
                            self.logger.error(f"最终卖出操作失败: {final_error}")
                
                # 记录执行时间
                execution_time = time.time() - start_time
                self.logger.info(f"交易执行完成，耗时: {execution_time:.2f}秒")
                self.logger.info("=" * 80)
                
                # 保存执行记录
                save_executed_trade(execution_record, self.symbol)
                
                return {
                    "success": True,
                    "tx_hash": tx_hash,
                    "amount_out": actual_amount,
                    "execution_record": execution_record
                }
                
            except Exception as e:
                error_msg = f"获取代币数量时出错: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
        except Exception as e:
            error_msg = f"执行买入操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'execution_record' in locals():
                execution_record['error'] = error_msg
            else:
                # 加载完整的代币地址信息
                token_addresses = load_token_addresses(self.symbol)

                execution_record = {
                    'symbol': self.symbol,
                    'chain': self.chain,
                    'eth_address': token_addresses.get('eth_address', ''),
                    'polygon_address': token_addresses.get('polygon_address', ''),
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'usdt_input': self.amount,
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "execution_record": execution_record
            }

    def execute_in_thread(self, result_queue: queue.Queue) -> None:
        """在新线程中执行买入操作"""
        try:
            # 检查当前线程是否已经有事件循环
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            try:
                # 执行买入操作
                result = loop.run_until_complete(self.execute_buy())
                
                # 将结果放入队列
                result_queue.put(result)
                
            except Exception as e:
                self.logger.error(f"执行买入操作时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
                result_queue.put({
                    "success": False,
                    "error": f"执行买入操作时出错: {str(e)}"
                })
                
        except Exception as e:
            self.logger.error(f"执行线程出错: {str(e)}")
            self.logger.error(traceback.format_exc())
            # 将错误结果放入队列
            result_queue.put({
                "success": False,
                "error": f"执行线程出错: {str(e)}"
            })

    async def bridge_tokens(self, token_amount: str) -> Dict:
        """
        执行代币桥接操作
        
        Args:
            token_amount: 要桥接的代币数量
            
        Returns:
            Dict: 桥接结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 桥接操作 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"桥接方向: {self.bridge_direction}")
            self.logger.info(f"代币数量: {token_amount} {self.symbol}")
            
            # 创建桥接记录
            bridge_record = {
                'symbol': self.symbol,
                'bridge_direction': self.bridge_direction,
                'token_amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': None
            }
            
            # 导入AutoBridge
            from src.bridge.pol_bridge.auto_bridge import AutoBridge

            # 创建AutoBridge实例（AutoBridge内部已有RPC故障转移机制）
            max_bridge_retries = 3
            bridge_retry_count = 0
            bridge = None

            while bridge_retry_count < max_bridge_retries:
                try:
                    self.logger.info(f"尝试初始化桥接 (第 {bridge_retry_count + 1} 次)")

                    # 创建AutoBridge实例（内部会自动处理RPC故障转移）
                    bridge = AutoBridge()

                    # 验证桥接初始化是否成功
                    if hasattr(bridge, 'bridge') and bridge.bridge:
                        self.logger.info("桥接初始化成功")
                        break
                    else:
                        raise ValueError("桥接初始化失败")

                except Exception as e:
                    bridge_retry_count += 1
                    self.logger.warning(f"桥接初始化失败 (第 {bridge_retry_count} 次): {str(e)}")

                    if bridge_retry_count >= max_bridge_retries:
                        error_msg = f"桥接初始化失败，已达到最大重试次数: {str(e)}"
                        self.logger.error(error_msg)
                        bridge_record['error'] = error_msg
                        return {
                            "success": False,
                            "error": error_msg,
                            "bridge_record": bridge_record
                        }

                    # 等待后重试
                    await asyncio.sleep(5)

            if not bridge:
                error_msg = "无法初始化桥接"
                self.logger.error(error_msg)
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }
            
            # 根据桥接方向执行不同的操作
            if self.bridge_direction == "ethereum_to_polygon":
                self.logger.info(f"从以太坊桥接到Polygon: {token_amount} {self.symbol}")
                # 使用 eth_to_polygon 方法
                result = await bridge.eth_to_polygon(
                    token_address=self.token_data["eth_address"],
                    amount=token_amount
                )
            elif self.bridge_direction == "polygon_to_ethereum":
                self.logger.info(f"从Polygon桥接到以太坊: {token_amount} {self.symbol}")
                # 使用 polygon_to_eth 方法
                result = await bridge.polygon_to_eth(
                    token_address=self.token_data["polygon_address"],
                    amount=token_amount
                )
            else:
                error_msg = f"无效的桥接方向: {self.bridge_direction}"
                self.logger.error(error_msg)
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }
            
            # 处理桥接结果
            if result.get("success"):
                self.logger.info("桥接操作成功完成")
                if self.bridge_direction == "ethereum_to_polygon":
                    self.logger.info(f"桥接交易哈希: {result.get('bridge_tx')}")
                    self.logger.info(f"Polygon交易哈希: {result.get('polygon_tx')}")
                    bridge_record.update({
                        'success': True,
                        'bridge_tx': result.get('bridge_tx'),
                        'polygon_tx': result.get('polygon_tx')
                    })
                else:
                    self.logger.info(f"Burn交易哈希: {result.get('burn_tx_hash')}")
                    self.logger.info(f"Claim交易哈希: {result.get('claim_tx_hash')}")
                    bridge_record.update({
                        'success': True,
                        'burn_tx_hash': result.get('burn_tx_hash'),
                        'claim_tx_hash': result.get('claim_tx_hash')
                    })
                
                # 保存桥接记录
                self.save_bridge_record(bridge_record)
                
                return {
                    "success": True,
                    "bridge_record": bridge_record
                }
            else:
                error_msg = result.get("error", "未知错误")
                self.logger.error(f"桥接操作失败: {error_msg}")
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }
                
        except Exception as e:
            error_msg = f"执行桥接操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'bridge_record' in locals():
                bridge_record['error'] = error_msg
            else:
                bridge_record = {
                    'symbol': self.symbol,
                    'bridge_direction': self.bridge_direction,
                    'token_amount': token_amount,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "bridge_record": bridge_record
            }

    async def handle_bridge_failure_recovery(self, token_amount: str) -> Dict:
        """
        处理桥接失败后的智能恢复机制

        Args:
            token_amount: 预期桥接的代币数量

        Returns:
            Dict: 恢复结果
        """
        try:
            self.logger.info(f"开始桥接失败恢复流程，代币数量: {token_amount}")

            # 获取钱包地址
            wallet_address = self.config.get('wallet', {}).get('address', '').lower()
            if not wallet_address:
                return {"success": False, "error": "无法获取钱包地址"}

            self.logger.info(f"使用钱包地址: {wallet_address}")

            # 重试循环
            max_retries = 10  # 最大重试次数
            retry_interval = 60  # 重试间隔60秒

            for retry_count in range(max_retries):
                self.logger.info(f"开始第 {retry_count + 1} 次恢复尝试...")

                # 1. 检查是否存在半完成的桥接交易
                half_completed_result = await self.check_half_completed_bridge(wallet_address, token_amount)

                if half_completed_result.get("found"):
                    self.logger.info("发现半完成的桥接交易，开始继续后续操作...")
                    continue_result = await self.continue_bridge_operation(half_completed_result)

                    if continue_result.get("success"):
                        return {
                            "success": True,
                            "bridge_record": continue_result.get("bridge_record", {}),
                            "recovery_type": "half_completed"
                        }
                    else:
                        self.logger.warning(f"继续桥接操作失败: {continue_result.get('error')}")

                # 2. 检查钱包余额
                balance_check = await self.check_wallet_balance(token_amount)

                if not balance_check.get("sufficient"):
                    self.logger.error(f"钱包余额不足，无法继续桥接: {balance_check.get('error')}")
                    return {"success": False, "error": "钱包余额不足"}

                # 3. 重新执行桥接操作
                self.logger.info("钱包余额充足，重新执行桥接操作...")
                bridge_result = await self.bridge_tokens(token_amount)

                if bridge_result.get("success"):
                    self.logger.info("重试桥接操作成功")
                    return {
                        "success": True,
                        "bridge_record": bridge_result.get("bridge_record", {}),
                        "recovery_type": "retry_success"
                    }
                else:
                    self.logger.warning(f"第 {retry_count + 1} 次桥接重试失败: {bridge_result.get('error')}")

                # 等待后重试
                if retry_count < max_retries - 1:
                    self.logger.info(f"等待 {retry_interval} 秒后进行下一次重试...")
                    await asyncio.sleep(retry_interval)

            return {"success": False, "error": f"经过 {max_retries} 次重试后仍然失败"}

        except Exception as e:
            error_msg = f"桥接恢复过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def check_half_completed_bridge(self, wallet_address: str, expected_amount: str) -> Dict:
        """
        检查是否存在半完成的桥接交易

        Args:
            wallet_address: 钱包地址
            expected_amount: 预期桥接的代币数量

        Returns:
            Dict: 检查结果
        """
        try:
            self.logger.info(f"检查半完成的桥接交易，钱包地址: {wallet_address}")

            # 根据桥接方向确定检查哪个链
            if self.bridge_direction == "ethereum_to_polygon":
                check_chain = "ethereum"
                check_minutes = 30
                token_address = self.token_data.get('eth_address', '')
            elif self.bridge_direction == "polygon_to_ethereum":
                check_chain = "polygon"
                check_minutes = 120
                token_address = self.token_data.get('polygon_address', '')
            else:
                return {"found": False, "error": "未知的桥接方向"}

            if not token_address:
                return {"found": False, "error": "无法获取代币地址"}

            # 导入monitor_token_tx模块
            try:
                import importlib.util
                import os
                spec = importlib.util.spec_from_file_location(
                    "monitor_token_tx",
                    os.path.join(os.path.dirname(__file__), "monitor_token_tx.py")
                )
                monitor_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(monitor_module)
            except Exception as e:
                self.logger.error(f"导入monitor_token_tx模块失败: {str(e)}")
                return {"found": False, "error": "无法导入监控模块"}

            # 获取交易记录
            try:
                web3 = monitor_module.get_web3_client(check_chain)
                if not isinstance(web3, list):
                    web3 = [web3]

                transactions = None
                for rpc in web3:
                    try:
                        transactions = monitor_module.get_token_transactions(token_address, check_chain, check_minutes)
                        if transactions is not None:
                            break
                    except Exception as e:
                        self.logger.warning(f"使用RPC获取交易记录失败: {str(e)}")
                        continue

                if transactions is None:
                    return {"found": False, "error": "无法获取交易记录"}

                # 检查是否有来自钱包地址的交易，且数量匹配
                expected_amount_float = float(expected_amount)
                tolerance = expected_amount_float * 0.01  # 1%容差

                for tx in transactions:
                    tx_from = tx.get('from', '').lower()
                    tx_value = tx.get('value', 0)

                    if tx_from == wallet_address:
                        # 检查交易金额是否匹配
                        if abs(tx_value - expected_amount_float) <= tolerance:
                            self.logger.info(f"发现匹配的桥接交易: {tx.get('hash')}")
                            return {
                                "found": True,
                                "tx_hash": tx.get('hash'),
                                "chain": check_chain,
                                "amount": tx_value,
                                "bridge_direction": self.bridge_direction
                            }

                return {"found": False, "reason": "未找到匹配的桥接交易"}

            except Exception as e:
                self.logger.error(f"检查交易记录时出错: {str(e)}")
                return {"found": False, "error": str(e)}

        except Exception as e:
            error_msg = f"检查半完成桥接交易时出错: {str(e)}"
            self.logger.error(error_msg)
            return {"found": False, "error": error_msg}

    async def continue_bridge_operation(self, half_completed_result: Dict) -> Dict:
        """
        继续未完成的桥接操作

        Args:
            half_completed_result: 半完成桥接交易的信息

        Returns:
            Dict: 继续操作的结果
        """
        try:
            bridge_direction = half_completed_result.get("bridge_direction")
            tx_hash = half_completed_result.get("tx_hash")

            self.logger.info(f"继续桥接操作，方向: {bridge_direction}, 交易哈希: {tx_hash}")

            if bridge_direction == "polygon_to_ethereum":
                # Polygon到以太坊：需要执行claim操作
                self.logger.info("执行claim操作...")

                try:
                    # 使用subprocess执行claim命令
                    import subprocess
                    cmd = [
                        "python", "-m", "src.bridge.pol_bridge.bridge_tokens",
                        "claim", "--tx-hash", tx_hash
                    ]

                    self.logger.info(f"执行命令: {' '.join(cmd)}")
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                    if result.returncode == 0:
                        self.logger.info("Claim操作成功完成")
                        return {
                            "success": True,
                            "bridge_record": {
                                "burn_tx_hash": tx_hash,
                                "claim_tx_hash": "completed_via_recovery",
                                "recovery_method": "claim_command"
                            }
                        }
                    else:
                        error_msg = f"Claim操作失败: {result.stderr}"
                        self.logger.error(error_msg)
                        return {"success": False, "error": error_msg}

                except Exception as e:
                    error_msg = f"执行claim命令时出错: {str(e)}"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg}

            elif bridge_direction == "ethereum_to_polygon":
                # 以太坊到Polygon：使用monitor_polygon_deposit方法
                self.logger.info("使用monitor_polygon_deposit方法监控Polygon到账...")

                try:
                    # 导入bridge_tokens模块
                    from src.bridge.pol_bridge.bridge_tokens import PolygonBridge

                    # 创建桥接实例
                    bridge = PolygonBridge()

                    # 获取以太坊代币地址和接收地址
                    ethereum_token_address = self.token_data.get('eth_address', '')
                    receiver_address = self.config.get('wallet', {}).get('address', '')

                    if not ethereum_token_address or not receiver_address:
                        return {"success": False, "error": "缺少必要的地址信息"}

                    # 监控Polygon到账
                    monitor_result = await bridge.monitor_polygon_deposit(
                        ethereum_token_address=ethereum_token_address,
                        receiver_address=receiver_address,
                        check_interval=10,
                        timeout_minutes=80,
                        expected_amount=float(half_completed_result.get("amount", 0)),
                        initial_wait_time=60  # 等待1分钟后开始监控
                    )

                    if monitor_result.get("success"):
                        self.logger.info("Polygon到账监控成功")
                        return {
                            "success": True,
                            "bridge_record": {
                                "bridge_tx": tx_hash,
                                "polygon_tx": monitor_result.get("polygon_tx", "completed_via_recovery"),
                                "recovery_method": "monitor_deposit"
                            }
                        }
                    else:
                        error_msg = f"Polygon到账监控失败: {monitor_result.get('error')}"
                        self.logger.error(error_msg)
                        return {"success": False, "error": error_msg}

                except Exception as e:
                    error_msg = f"监控Polygon到账时出错: {str(e)}"
                    self.logger.error(error_msg)
                    return {"success": False, "error": error_msg}

            else:
                return {"success": False, "error": f"不支持的桥接方向: {bridge_direction}"}

        except Exception as e:
            error_msg = f"继续桥接操作时出错: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def check_wallet_balance(self, required_amount: str) -> Dict:
        """
        检查钱包代币余额是否足够

        Args:
            required_amount: 需要的代币数量

        Returns:
            Dict: 余额检查结果
        """
        try:
            self.logger.info(f"检查钱包余额，需要数量: {required_amount}")

            # 根据桥接方向确定检查哪个链的余额
            if self.bridge_direction == "ethereum_to_polygon":
                chain = "ethereum"
                token_address = self.token_data.get('eth_address', '')
            elif self.bridge_direction == "polygon_to_ethereum":
                chain = "polygon"
                token_address = self.token_data.get('polygon_address', '')
            else:
                return {"sufficient": False, "error": "未知的桥接方向"}

            if not token_address:
                return {"sufficient": False, "error": "无法获取代币地址"}

            wallet_address = self.config.get('wallet', {}).get('address', '')
            if not wallet_address:
                return {"sufficient": False, "error": "无法获取钱包地址"}

            # 获取私钥
            private_key = self.config.get('wallet', {}).get('private_key', '')
            if not private_key:
                return {"sufficient": False, "error": "无法获取私钥"}

            # 导入相应的客户端并传入私钥
            try:
                if chain == "ethereum":
                    from src.dex.KyberSwap.client import KyberSwapClient
                    client = KyberSwapClient("ethereum", private_key)
                elif chain == "polygon":
                    from src.dex.KyberSwap.client import KyberSwapClient
                    client = KyberSwapClient("polygon", private_key)
                else:
                    return {"sufficient": False, "error": f"不支持的链: {chain}"}

                # 获取代币余额
                balance = client.get_balance(token_address)
                required_amount_float = float(required_amount)

                self.logger.info(f"钱包余额: {balance}, 需要数量: {required_amount_float}")

                if balance >= required_amount_float:
                    return {
                        "sufficient": True,
                        "balance": balance,
                        "required": required_amount_float
                    }
                else:
                    return {
                        "sufficient": False,
                        "balance": balance,
                        "required": required_amount_float,
                        "error": f"余额不足: {balance} < {required_amount_float}"
                    }

            except Exception as e:
                # 如果获取余额失败，重试几次
                max_balance_retries = 3
                for retry in range(max_balance_retries):
                    self.logger.warning(f"获取余额失败，第 {retry + 1} 次重试: {str(e)}")
                    await asyncio.sleep(10)  # 等待10秒后重试

                    try:
                        # 重新创建客户端并传入私钥
                        private_key = self.config.get('wallet', {}).get('private_key', '')
                        if not private_key:
                            continue  # 跳过这次重试

                        if chain == "ethereum":
                            from src.dex.KyberSwap.client import KyberSwapClient
                            client = KyberSwapClient("ethereum", private_key)
                        elif chain == "polygon":
                            from src.dex.KyberSwap.client import KyberSwapClient
                            client = KyberSwapClient("polygon", private_key)

                        balance = client.get_balance(token_address)
                        required_amount_float = float(required_amount)

                        if balance >= required_amount_float:
                            return {
                                "sufficient": True,
                                "balance": balance,
                                "required": required_amount_float
                            }
                        else:
                            return {
                                "sufficient": False,
                                "balance": balance,
                                "required": required_amount_float,
                                "error": f"余额不足: {balance} < {required_amount_float}"
                            }
                    except Exception as retry_e:
                        if retry == max_balance_retries - 1:
                            return {"sufficient": False, "error": f"获取余额失败: {str(retry_e)}"}
                        continue

        except Exception as e:
            error_msg = f"检查钱包余额时出错: {str(e)}"
            self.logger.error(error_msg)
            return {"sufficient": False, "error": error_msg}

    def save_bridge_record(self, record: dict) -> None:
        """
        保存桥接记录
        
        Args:
            record: 桥接记录
        """
        try:
            # 创建桥接记录目录
            bridge_dir = os.path.join(os.path.dirname(__file__), "results", "bridge")
            os.makedirs(bridge_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.symbol}_{timestamp}_bridge.json"
            filepath = os.path.join(bridge_dir, filename)
            
            # 保存记录
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record, f, indent=4, ensure_ascii=False)
                
            self.logger.info(f"桥接记录已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存桥接记录时出错: {str(e)}")
            self.logger.error(traceback.format_exc())

    async def find_optimal_sell_chain(self, token_amount: str) -> Dict:
        """
        找到最优的卖出链，比较两个链的输出并考虑跨链成本

        Args:
            token_amount: 要卖出的代币数量

        Returns:
            Dict: 包含最优链信息和卖出决策的结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始寻找 {self.symbol} 的最优卖出链 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"代币数量: {token_amount} {self.symbol}")

            # 导入bridge_arb_finder模块
            from scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder import BridgeArbFinder

            # 创建finder实例
            finder = BridgeArbFinder()

            # 获取两个链的代币地址
            polygon_address = self.token_data.get('polygon_address', '')
            eth_address = self.token_data.get('eth_address', '')

            if not polygon_address or not eth_address:
                return {
                    "success": False,
                    "error": "缺少代币地址信息",
                    "optimal_chain": None
                }

            self.logger.info(f"Polygon地址: {polygon_address}")
            self.logger.info(f"Ethereum地址: {eth_address}")

            # 将token_amount转换为float
            token_amount_float = float(token_amount)

            # 1. 检查两个链的卖出价格
            self.logger.info("检查Polygon链卖出价格...")
            polygon_sell_result = await finder.check_token_to_usdt(
                token_address=polygon_address,
                chain="polygon",
                token_amount=token_amount_float
            )

            self.logger.info("检查Ethereum链卖出价格...")
            eth_sell_result = await finder.check_token_to_usdt(
                token_address=eth_address,
                chain="ethereum",
                token_amount=token_amount_float
            )

            # 检查结果并获取gas成本
            if not polygon_sell_result.get("success"):
                self.logger.error(f"Polygon链卖出价格检查失败: {polygon_sell_result.get('error', '未知错误')}")
                polygon_usdt_out = 0
                polygon_gas_cost = 0
            else:
                polygon_usdt_out = polygon_sell_result.get("usdt_amount", 0)
                polygon_gas_cost = polygon_sell_result.get("gas_cost_usd", 0)
                self.logger.info(f"Polygon链预期输出: {polygon_usdt_out} USDT, Gas成本: {polygon_gas_cost} USDT")

            if not eth_sell_result.get("success"):
                self.logger.error(f"Ethereum链卖出价格检查失败: {eth_sell_result.get('error', '未知错误')}")
                eth_usdt_out = 0
                eth_gas_cost = 0
            else:
                eth_usdt_out = eth_sell_result.get("usdt_amount", 0)
                eth_gas_cost = eth_sell_result.get("gas_cost_usd", 0)
                self.logger.info(f"Ethereum链预期输出: {eth_usdt_out} USDT, Gas成本: {eth_gas_cost} USDT")

            # 如果两个链都失败，返回错误
            if polygon_usdt_out == 0 and eth_usdt_out == 0:
                return {
                    "success": False,
                    "error": "两个链的卖出价格检查都失败",
                    "optimal_chain": None
                }

            # 2. 比较输出差异
            usdt_diff = abs(polygon_usdt_out - eth_usdt_out)
            self.logger.info(f"两链USDT输出差异: {usdt_diff} USDT")

            # 确定当前链和另一条链
            current_chain = "polygon" if self.bridge_direction == "ethereum_to_polygon" else "ethereum"
            other_chain = "ethereum" if current_chain == "polygon" else "polygon"

            current_chain_usdt = polygon_usdt_out if current_chain == "polygon" else eth_usdt_out
            other_chain_usdt = eth_usdt_out if current_chain == "polygon" else polygon_usdt_out

            # 获取对应的gas成本
            current_chain_gas_cost = polygon_gas_cost if current_chain == "polygon" else eth_gas_cost
            other_chain_gas_cost = eth_gas_cost if current_chain == "polygon" else polygon_gas_cost

            self.logger.info(f"当前链({current_chain})输出: {current_chain_usdt} USDT, Gas成本: {current_chain_gas_cost} USDT")
            self.logger.info(f"另一条链({other_chain})输出: {other_chain_usdt} USDT, Gas成本: {other_chain_gas_cost} USDT")

            # 3. 决策逻辑
            if usdt_diff < 2:
                # 差异小于2 USDT，直接在当前链执行
                self.logger.info(f"输出差异({usdt_diff} USDT)小于2 USDT，选择在当前链({current_chain})执行卖出")
                return {
                    "success": True,
                    "optimal_chain": current_chain,
                    "reason": f"输出差异({usdt_diff:.2f} USDT)小于2 USDT",
                    "current_chain_usdt": current_chain_usdt,
                    "other_chain_usdt": other_chain_usdt,
                    "current_chain_gas_cost": current_chain_gas_cost,
                    "other_chain_gas_cost": other_chain_gas_cost,
                    "bridge_needed": False
                }
            else:
                # 差异大于2 USDT，需要考虑跨链成本
                self.logger.info(f"输出差异({usdt_diff} USDT)大于2 USDT，计算跨链成本...")

                # 4. 计算跨链成本
                bridge_direction = f"{current_chain}_to_{other_chain}"
                bridge_cost_result = await finder.estimate_bridge_gas_costs(bridge_direction, self.symbol)
                bridge_cost_usd = bridge_cost_result.get("gas_cost_usd", 0)

                self.logger.info(f"跨链成本: {bridge_cost_usd} USDT")

                # 5. 比较净收益（包含swap gas成本）
                # 当前链净收益 = 输出 - swap gas成本
                current_chain_net = current_chain_usdt - current_chain_gas_cost
                # 另一条链净收益 = 输出 - swap gas成本 - 跨链成本
                other_chain_net = other_chain_usdt - other_chain_gas_cost - bridge_cost_usd

                self.logger.info(f"当前链净收益: {current_chain_net:.4f} USDT (输出: {current_chain_usdt} - Gas: {current_chain_gas_cost})")
                self.logger.info(f"另一条链净收益: {other_chain_net:.4f} USDT (输出: {other_chain_usdt} - Gas: {other_chain_gas_cost} - 跨链: {bridge_cost_usd})")

                # 6. 最终决策
                net_diff = other_chain_net - current_chain_net

                if net_diff > 1.5:
                    # 另一条链净收益高出1.5 USDT以上，选择跨链
                    self.logger.info(f"另一条链净收益高出{net_diff:.2f} USDT > 1.5 USDT，选择跨链到{other_chain}执行卖出")
                    return {
                        "success": True,
                        "optimal_chain": other_chain,
                        "reason": f"另一条链净收益高出{net_diff:.2f} USDT > 1.5 USDT",
                        "current_chain_usdt": current_chain_usdt,
                        "other_chain_usdt": other_chain_usdt,
                        "current_chain_gas_cost": current_chain_gas_cost,
                        "other_chain_gas_cost": other_chain_gas_cost,
                        "bridge_cost": bridge_cost_usd,
                        "current_chain_net": current_chain_net,
                        "other_chain_net": other_chain_net,
                        "bridge_needed": True
                    }
                else:
                    # 差异不足1.5 USDT，在当前链执行
                    self.logger.info(f"另一条链净收益仅高出{net_diff:.2f} USDT <= 1.5 USDT，选择在当前链({current_chain})执行卖出")
                    return {
                        "success": True,
                        "optimal_chain": current_chain,
                        "reason": f"另一条链净收益仅高出{net_diff:.2f} USDT <= 1.5 USDT",
                        "current_chain_usdt": current_chain_usdt,
                        "other_chain_usdt": other_chain_usdt,
                        "current_chain_gas_cost": current_chain_gas_cost,
                        "other_chain_gas_cost": other_chain_gas_cost,
                        "bridge_cost": bridge_cost_usd,
                        "current_chain_net": current_chain_net,
                        "other_chain_net": other_chain_net,
                        "bridge_needed": False
                    }

        except Exception as e:
            error_msg = f"寻找最优卖出链时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": error_msg,
                "optimal_chain": None
            }

    async def execute_sell_with_optimal_chain(self, token_amount: str) -> Dict:
        """
        使用最优链选择策略执行代币卖出操作

        Args:
            token_amount: 要卖出的代币数量

        Returns:
            Dict: 卖出结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 最优链卖出交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 1. 寻找最优卖出链
            optimal_result = await self.find_optimal_sell_chain(token_amount)

            if not optimal_result.get("success"):
                error_msg = optimal_result.get("error", "寻找最优链失败")
                self.logger.error(f"寻找最优卖出链失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }

            optimal_chain = optimal_result.get("optimal_chain")
            bridge_needed = optimal_result.get("bridge_needed", False)

            self.logger.info(f"最优卖出链: {optimal_chain}")
            self.logger.info(f"选择原因: {optimal_result.get('reason', '未知')}")
            self.logger.info(f"是否需要跨链: {'是' if bridge_needed else '否'}")

            # 2. 如果需要跨链，执行跨链操作
            if bridge_needed:
                self.logger.info("🌉 检测到需要跨链，开始执行跨链操作...")
                self.logger.info(f"目标最优链: {optimal_chain}")

                # 确定当前链
                current_chain = "polygon" if self.bridge_direction == "ethereum_to_polygon" else "ethereum"
                self.logger.info(f"当前链: {current_chain}, 目标链: {optimal_chain}")

                # 执行跨链操作
                bridge_result = await self.execute_cross_chain_bridge(token_amount, current_chain, optimal_chain)

                if not bridge_result.get("success"):
                    error_msg = bridge_result.get("error", "跨链失败")
                    self.logger.error(f"跨链操作失败: {error_msg}")
                    self.logger.warning("跨链失败，回退到当前链执行卖出")
                    optimal_chain = current_chain
                else:
                    self.logger.info("✅ 跨链操作成功完成")
                    # 跨链成功后，代币已经在目标链上，可以直接在目标链执行卖出

            # 3. 在最优链上执行卖出
            target_chain = optimal_chain

            # 获取配置
            config = load_config()
            if not config:
                error_msg = "无法加载配置文件"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 获取私钥
            private_key = config.get("dex", {}).get(target_chain, {}).get("wallet", {}).get("private_key", "")
            if not private_key:
                error_msg = f"无法获取{target_chain}链的私钥"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 获取目标链的代币地址
            if target_chain == "polygon":
                target_token_address = self.token_data.get('polygon_address', '')
                if not target_token_address:
                    return {
                        "success": False,
                        "error": "未找到Polygon链上的代币地址"
                    }
            else:  # ethereum
                target_token_address = self.token_data.get('eth_address', '')
                if not target_token_address:
                    return {
                        "success": False,
                        "error": "未找到以太坊链上的代币地址"
                    }

            self.logger.info(f"最终执行链: {target_chain}")
            self.logger.info(f"代币地址: {target_token_address}")
            self.logger.info(f"卖出数量: {token_amount} {self.symbol}")

            # 创建卖出记录
            sell_record = {
                'symbol': self.symbol,
                'chain': target_chain,
                'token_address': target_token_address,
                'token_amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': None,
                'optimal_chain_analysis': optimal_result  # 记录最优链分析结果
            }

            # 获取USDT地址 - 使用硬编码地址避免配置文件问题
            polygon_usdt = "******************************************"  # Polygon USDT
            eth_usdt = "******************************************"  # Ethereum USDT
            target_usdt = polygon_usdt if target_chain == "polygon" else eth_usdt

            # 添加重试逻辑处理"未知"状态
            max_sell_retries = 3
            sell_success = False
            swap_result = None

            for sell_attempt in range(max_sell_retries):
                try:
                    if sell_attempt > 0:
                        self.logger.info(f"第{sell_attempt + 1}次尝试在最优链执行卖出交易...")

                    # 调用swap_tokens执行卖出交易（不再使用预期输出验证）
                    self.logger.info("在最优链执行卖出交易（无预期输出验证）")

                    swap_result = await swap_tokens(
                        chain=target_chain,
                        token_in=target_token_address,
                        token_out=target_usdt,
                        amount=float(token_amount),
                        slippage=0.5,
                        timeout=1200,
                        gas_multiplier=1.8,
                        save_gas=True,
                        real=True,  # 执行实际交易
                        expected_usdt_out=None  # 不再使用预期输出验证
                    )

                    if swap_result.get("success"):
                        sell_success = True
                        break
                    else:
                        # 获取错误信息，处理None值的情况
                        error_msg = swap_result.get("error") or "未知错误"
                        status = swap_result.get("status") or "失败"

                        # 记录详细的swap_result信息用于调试
                        self.logger.error(f"最优链卖出交易执行失败: {error_msg}")
                        self.logger.error(f"swap_result详细信息: {swap_result}")

                        # 检查是否需要重试（仅对"未知"状态重试）
                        if status == "未知":
                            self.logger.warning(f"第{sell_attempt + 1}次最优链卖出失败，状态: {status}, 错误: {error_msg}")

                            if sell_attempt < max_sell_retries - 1:
                                # 线性递增重试时间：20-88秒
                                retry_delay = 20 + (sell_attempt * 34)  # 20, 54, 88
                                self.logger.info(f"等待{retry_delay}秒后重试最优链卖出...")
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                self.logger.error(f"最优链卖出在{max_sell_retries}次尝试后仍然失败: {error_msg}")
                                sell_record['error'] = error_msg
                                break
                        else:
                            # 非"未知"状态的错误，不重试
                            sell_record['error'] = error_msg
                            break

                except Exception as e:
                    error_msg = f"最优链卖出操作异常: {str(e)}"
                    self.logger.error(error_msg)
                    self.logger.error(traceback.format_exc())
                    sell_record['error'] = error_msg
                    break

            if not sell_success:
                final_error = swap_result.get("error", "未知错误") if swap_result else "最优链卖出操作失败"
                return {
                    "success": False,
                    "error": final_error,
                    "sell_record": sell_record
                }

            # 获取交易结果
            tx_hash = swap_result.get("tx_hash")

            # 获取实际卖出的USDT数量
            try:
                # 创建目标链的KyberSwap客户端
                target_client = KyberSwapClient(chain=target_chain)

                # 使用目标链的web3实例获取钱包地址
                web3 = target_client.web3
                account = web3.eth.account.from_key(private_key)
                user_address = account.address

                # 添加重试逻辑获取USDT数量
                max_retries = 5
                last_error = None
                token_result = None

                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            self.logger.info(f"第{attempt + 1}次尝试获取USDT数量...")

                        # 使用tx_token_change_tracker获取USDT数量
                        self.logger.info(f"使用tx_token_change_tracker获取交易 {tx_hash} 的USDT数量...")

                        token_result = get_last_received_token(
                            tx_hash=tx_hash,
                            chain=target_chain,
                            user_address=user_address
                        )

                        if "error" not in token_result:
                            self.logger.info(f"从tx_token_change_tracker成功获取到USDT数量")
                            break
                        else:
                            last_error = token_result["error"]
                            self.logger.warning(f"第{attempt + 1}次获取USDT数量失败: {last_error}")
                            if attempt < max_retries - 1:
                                # 线性递增重试时间：10-120秒
                                retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                                self.logger.info(f"等待{retry_delay}秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue

                    except Exception as e:
                        last_error = str(e)
                        self.logger.warning(f"第{attempt + 1}次获取USDT数量出错: {last_error}")
                        if attempt < max_retries - 1:
                            # 线性递增重试时间：10-120秒
                            retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                            self.logger.info(f"等待{retry_delay}秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue

                if "error" in token_result:
                    error_msg = f"在{max_retries}次尝试后仍然无法获取USDT数量: {last_error}"
                    self.logger.error(error_msg)
                    sell_record['error'] = error_msg
                    return {
                        "success": False,
                        "error": error_msg,
                        "sell_record": sell_record
                    }

                # 获取实际收到的USDT数量
                usdt_amount_str = str(token_result["amount"])
                self.logger.info(f"成功获取到USDT数量: {usdt_amount_str}")

                # 更新卖出记录
                sell_record.update({
                    'success': True,
                    'tx_hash': tx_hash,
                    'usdt_received': usdt_amount_str,
                    'price': float(usdt_amount_str) / float(token_amount) if float(token_amount) > 0 else 0
                })

                self.logger.info("最优链卖出交易执行成功:")
                self.logger.info(f"  执行链: {target_chain}")
                self.logger.info(f"  交易哈希: {tx_hash}")
                self.logger.info(f"  实际收到USDT数量: {usdt_amount_str}")

                # 保存卖出记录
                self.save_sell_record(sell_record)

                return {
                    "success": True,
                    "tx_hash": tx_hash,
                    "usdt_received": usdt_amount_str,
                    "optimal_chain": target_chain,
                    "sell_record": sell_record
                }

            except Exception as e:
                error_msg = f"获取USDT数量时出错: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                sell_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "sell_record": sell_record
                }

        except Exception as e:
            error_msg = f"执行最优链卖出操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())

            return {
                "success": False,
                "error": error_msg
            }

    async def execute_cross_chain_bridge(self, token_amount: str, from_chain: str, to_chain: str) -> Dict:
        """
        执行跨链桥接操作

        Args:
            token_amount: 代币数量（字符串格式）
            from_chain: 源链 (ethereum 或 polygon)
            to_chain: 目标链 (ethereum 或 polygon)

        Returns:
            Dict: 桥接结果
        """
        try:
            self.logger.info(f"🌉 开始跨链桥接: {token_amount} {self.symbol} 从 {from_chain} 到 {to_chain}")

            # 创建桥接记录
            bridge_record = {
                'symbol': self.symbol,
                'from_chain': from_chain,
                'to_chain': to_chain,
                'amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'bridge_tx': '',
                'claim_tx': '',
                'error': None
            }

            # 获取源链和目标链的代币地址
            if from_chain == "ethereum" and to_chain == "polygon":
                source_token_address = self.token_data.get("eth_address")
                target_token_address = self.token_data.get("polygon_address")
                bridge_direction = "eth_to_polygon"
            elif from_chain == "polygon" and to_chain == "ethereum":
                source_token_address = self.token_data.get("polygon_address")
                target_token_address = self.token_data.get("eth_address")
                bridge_direction = "polygon_to_eth"
            else:
                error_msg = f"不支持的跨链方向: {from_chain} -> {to_chain}"
                self.logger.error(error_msg)
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }

            if not source_token_address:
                error_msg = f"无法获取{from_chain}链上的代币地址"
                self.logger.error(error_msg)
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }

            self.logger.info(f"源链代币地址: {source_token_address}")
            self.logger.info(f"目标链代币地址: {target_token_address}")

            # 创建AutoBridge实例
            bridge = AutoBridge()

            # 根据桥接方向执行不同的操作
            if bridge_direction == "eth_to_polygon":
                self.logger.info(f"从以太坊桥接到Polygon: {token_amount} {self.symbol}")
                result = await bridge.eth_to_polygon(
                    token_address=source_token_address,
                    amount=float(token_amount)
                )
            elif bridge_direction == "polygon_to_eth":
                self.logger.info(f"从Polygon桥接到以太坊: {token_amount} {self.symbol}")
                result = await bridge.polygon_to_eth(
                    token_address=source_token_address,
                    amount=float(token_amount)
                )

            # 检查桥接结果
            if not result.get("success"):
                error_msg = result.get("message", "桥接失败")
                self.logger.error(f"桥接操作失败: {error_msg}")
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }

            # 更新桥接记录
            bridge_record.update({
                'success': True,
                'bridge_tx': result.get('bridge_tx', result.get('burn_tx', '')),
                'claim_tx': result.get('claim_tx', ''),
                'message': result.get('message', '桥接成功')
            })

            self.logger.info("✅ 跨链桥接操作成功完成")
            self.logger.info(f"桥接交易哈希: {bridge_record['bridge_tx']}")
            if bridge_record['claim_tx']:
                self.logger.info(f"认领交易哈希: {bridge_record['claim_tx']}")

            return {
                "success": True,
                "bridge_record": bridge_record,
                "message": "跨链桥接成功"
            }

        except Exception as e:
            error_msg = f"跨链桥接过程中发生异常: {str(e)}"
            self.logger.error(error_msg)
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")

            bridge_record['error'] = error_msg
            return {
                "success": False,
                "error": error_msg,
                "bridge_record": bridge_record
            }

    async def execute_sell(self, token_amount: str) -> Dict:
        """
        在目标链上执行代币卖出操作

        Args:
            token_amount: 要卖出的代币数量

        Returns:
            Dict: 卖出结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 卖出交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 确定目标链和代币地址
            target_chain = "polygon" if self.bridge_direction == "ethereum_to_polygon" else "ethereum"

            # 获取配置
            config = load_config()
            if not config:
                error_msg = "无法加载配置文件"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 获取私钥
            private_key = config.get("dex", {}).get(target_chain, {}).get("wallet", {}).get("private_key", "")
            if not private_key:
                error_msg = f"无法获取{target_chain}链的私钥"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 直接使用 token_data 中的地址
            target_token_address = None
            if self.bridge_direction == "ethereum_to_polygon":
                # 从以太坊到Polygon，使用Polygon地址
                target_token_address = self.token_data.get("polygon_address")
            else:
                # 从Polygon到以太坊，使用以太坊地址
                target_token_address = self.token_data.get("eth_address")

            if not target_token_address:
                error_msg = f"无法获取目标链上的代币地址: {self.symbol}"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            self.logger.info(f"目标链: {target_chain}")
            self.logger.info(f"目标链代币地址: {target_token_address}")

            # 创建卖出记录
            sell_record = {
                'symbol': self.symbol,
                'chain': target_chain,
                'token_address': target_token_address,
                'token_amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': None,
                'expected_usdt_out': self.expected_usdt_out  # 记录预期输出
            }

            self.logger.info(f"在{target_chain}链上执行卖出操作")
            self.logger.info(f"代币地址: {target_token_address}")
            self.logger.info(f"卖出数量: {token_amount} {self.symbol}")
            if self.expected_usdt_out:
                self.logger.info(f"预期USDT输出: {self.expected_usdt_out} USDT")

            # 创建目标链的KyberSwap客户端
            target_client = KyberSwapClient(chain=target_chain)

            # 获取目标链的USDT地址
            target_usdt = self.polygon_usdt if target_chain == "polygon" else self.eth_usdt

            # 调用swap_tokens执行卖出交易（包含预验证逻辑）
            if self.expected_usdt_out and self.expected_usdt_out > 0:
                self.logger.info(f"执行卖出交易，预期USDT输出: {self.expected_usdt_out}")
            else:
                self.logger.info("执行卖出交易（无预期输出验证）")

            swap_result = await swap_tokens(
                chain=target_chain,
                token_in=target_token_address,
                token_out=target_usdt,
                amount=float(token_amount),
                slippage=0.5,
                timeout=1200,
                gas_multiplier=1.8,
                save_gas=True,
                real=True,  # 执行实际交易
                expected_usdt_out=self.expected_usdt_out  # 传递预期USDT输出用于预验证
            )
            
            if not swap_result.get("success"):
                # 获取错误信息，处理None值的情况
                error_msg = swap_result.get("error") or "未知错误"
                status = swap_result.get("status") or "失败"

                # 记录详细的swap_result信息用于调试
                self.logger.error(f"swap_result详细信息: {swap_result}")
                self.logger.error(f"解析后的错误信息: error_msg='{error_msg}', status='{status}'")

                # 检查是否为预验证失败
                if status in ["预验证失败", "输出不足", "预验证异常"]:
                    self.logger.warning(f"卖出预验证失败: {error_msg}")
                    self.logger.info("预验证失败，启动最优链选择策略...")

                    # 调用最优链选择策略
                    optimal_sell_result = await self.execute_sell_with_optimal_chain(token_amount)

                    if optimal_sell_result.get("success"):
                        self.logger.info("最优链选择策略执行成功")
                        return optimal_sell_result
                    else:
                        self.logger.error(f"最优链选择策略也失败: {optimal_sell_result.get('error', '未知错误')}")

                        # 如果最优链策略也失败，返回原始的预验证失败信息
                        if "predicted_usdt_out" in swap_result:
                            sell_record['predicted_usdt_out'] = swap_result['predicted_usdt_out']
                        if "min_acceptable_usdt" in swap_result:
                            sell_record['min_acceptable_usdt'] = swap_result['min_acceptable_usdt']
                        if "expected_usdt_out" in swap_result:
                            sell_record['expected_usdt_out'] = swap_result['expected_usdt_out']

                        sell_record['error'] = error_msg
                        sell_record['status'] = status
                        sell_record['optimal_chain_fallback_error'] = optimal_sell_result.get('error')

                        return {
                            "success": False,
                            "error": error_msg,
                            "status": status,
                            "predicted_usdt_out": swap_result.get("predicted_usdt_out"),
                            "optimal_chain_fallback_error": optimal_sell_result.get('error'),
                            "sell_record": sell_record
                        }
                elif status == "未知":
                    # 对于"未知"状态，进行重试
                    self.logger.error(f"卖出交易执行失败: {error_msg}")
                    self.logger.error(f"卖出操作失败: {error_msg}")
                    self.logger.info("检测到'未知'状态，启动重试机制...")

                    # 返回特殊标识，让上层调用者进行重试
                    sell_record['error'] = error_msg
                    sell_record['status'] = status
                    return {
                        "success": False,
                        "error": error_msg,
                        "status": status,
                        "need_retry": True,  # 标识需要重试
                        "sell_record": sell_record
                    }
                else:
                    self.logger.error(f"卖出交易执行失败: {error_msg}")
                    sell_record['error'] = error_msg
                    return {
                        "success": False,
                        "error": error_msg,
                        "sell_record": sell_record
                    }
            
            # 获取交易结果
            tx_hash = swap_result.get("tx_hash")
            
            # 获取实际卖出的USDT数量
            try:
                # 使用目标链的web3实例获取钱包地址
                web3 = target_client.web3
                account = web3.eth.account.from_key(private_key)
                user_address = account.address
                
                # 添加重试逻辑获取USDT数量
                max_retries = 5
                last_error = None
                token_result = None

                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            self.logger.info(f"第{attempt + 1}次尝试获取USDT数量...")

                        # 使用tx_token_change_tracker获取USDT数量
                        self.logger.info(f"使用tx_token_change_tracker获取交易 {tx_hash} 的USDT数量...")

                        token_result = get_last_received_token(
                            tx_hash=tx_hash,
                            chain=target_chain,
                            user_address=user_address  # 使用钱包地址而不是代币地址
                        )

                        if "error" not in token_result:
                            self.logger.info(f"从tx_token_change_tracker成功获取到USDT数量")
                            break
                        else:
                            last_error = token_result["error"]
                            self.logger.warning(f"第{attempt + 1}次获取USDT数量失败: {last_error}")
                            if attempt < max_retries - 1:
                                # 线性递增重试时间：10-120秒
                                retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                                self.logger.info(f"等待{retry_delay}秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue

                    except Exception as e:
                        last_error = str(e)
                        self.logger.warning(f"第{attempt + 1}次获取USDT数量出错: {last_error}")
                        if attempt < max_retries - 1:
                            # 线性递增重试时间：10-120秒
                            retry_delay = 10 + (attempt * 27.5)  # 10, 37.5, 65, 92.5, 120
                            self.logger.info(f"等待{retry_delay}秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue

                if "error" in token_result:
                    error_msg = f"在{max_retries}次尝试后仍然无法获取USDT数量: {last_error}"
                    self.logger.error(error_msg)
                    sell_record['error'] = error_msg
                    return {
                        "success": False,
                        "error": error_msg,
                        "sell_record": sell_record
                    }
                
                # 获取实际收到的USDT数量
                usdt_amount_str = str(token_result["amount"])
                self.logger.info(f"成功获取到USDT数量: {usdt_amount_str}")
                
                # 更新卖出记录
                sell_record.update({
                    'success': True,
                    'tx_hash': tx_hash,
                    'usdt_received': usdt_amount_str,
                    'price': float(usdt_amount_str) / float(token_amount) if float(token_amount) > 0 else 0
                })
                
                self.logger.info("卖出交易执行成功:")
                self.logger.info(f"  交易哈希: {tx_hash}")
                self.logger.info(f"  实际收到USDT数量: {usdt_amount_str}")
                
                # 保存卖出记录
                self.save_sell_record(sell_record)
                
                return {
                    "success": True,
                    "tx_hash": tx_hash,
                    "usdt_received": usdt_amount_str,
                    "sell_record": sell_record
                }
                
            except Exception as e:
                error_msg = f"获取USDT数量时出错: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                sell_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "sell_record": sell_record
                }
            
        except Exception as e:
            error_msg = f"执行卖出操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'sell_record' in locals():
                sell_record['error'] = error_msg
            else:
                sell_record = {
                    'symbol': self.symbol,
                    'chain': target_chain,
                    'token_address': target_token_address,
                    'token_amount': token_amount,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "sell_record": sell_record
            }
    
    def save_sell_record(self, record: dict) -> None:
        """
        保存卖出记录
        
        Args:
            record: 卖出记录
        """
        try:
            # 创建卖出记录目录
            sell_dir = os.path.join(os.path.dirname(__file__), "results", "sell")
            os.makedirs(sell_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.symbol}_{timestamp}_sell.json"
            filepath = os.path.join(sell_dir, filename)
            
            # 保存记录
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record, f, indent=4, ensure_ascii=False)
                
            self.logger.info(f"卖出记录已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存卖出记录时出错: {str(e)}")
            self.logger.error(traceback.format_exc())

def load_token_addresses(symbol: str) -> dict:
    """
    从tokens.json文件中加载代币地址

    Args:
        symbol: 代币符号

    Returns:
        dict: 包含eth_address和polygon_address的字典
    """
    try:
        tokens_file = os.path.join(os.path.dirname(__file__), "..", "..", "..", "src", "bridge", "pol_bridge", "data", "tokens.json")

        if os.path.exists(tokens_file):
            with open(tokens_file, 'r', encoding='utf-8') as f:
                tokens_data = json.load(f)

            if symbol in tokens_data:
                token_info = tokens_data[symbol]
                eth_address = token_info.get("1", {}).get("address", "")
                polygon_address = token_info.get("137", {}).get("address", "")

                return {
                    "eth_address": eth_address,
                    "polygon_address": polygon_address
                }
    except Exception as e:
        logging.error(f"加载代币地址时出错: {str(e)}")

    return {"eth_address": "", "polygon_address": ""}

def remove_all_blacklisted_from_zero_tx_stats(blacklist_eth_addresses: list) -> None:
    """
    从zero_tx_stats_detailed.json中删除黑名单中所有代币的数据

    Args:
        blacklist_eth_addresses: 黑名单中的所有以太坊地址列表
    """
    try:
        zero_tx_file = os.path.join(os.path.dirname(__file__), "..", "..", "..", "src", "bridge", "pol_bridge", "data", "zero_tx_stats_detailed.json")

        if not os.path.exists(zero_tx_file):
            logging.warning(f"zero_tx_stats_detailed.json 文件不存在: {zero_tx_file}")
            return

        # 读取现有数据
        with open(zero_tx_file, 'r', encoding='utf-8') as f:
            zero_tx_data = json.load(f)

        original_count = len(zero_tx_data)

        # 将黑名单地址转换为小写，便于比较
        blacklist_addresses_lower = [addr.lower() for addr in blacklist_eth_addresses]

        # 查找并删除匹配的条目
        keys_to_delete = []
        deleted_details = []

        for polygon_address, token_data in zero_tx_data.items():
            if isinstance(token_data, dict):
                token_eth_address = token_data.get('eth_address', '').lower()
                if token_eth_address in blacklist_addresses_lower:
                    keys_to_delete.append(polygon_address)
                    token_symbol = token_data.get('symbol', 'Unknown')
                    token_name = token_data.get('name', 'Unknown')
                    tx_count = len(token_data.get('transactions', []))
                    deleted_details.append({
                        'symbol': token_symbol,
                        'name': token_name,
                        'polygon_address': polygon_address,
                        'eth_address': token_data.get('eth_address', ''),
                        'tx_count': tx_count
                    })
                    logging.info(f"标记删除: {token_symbol} ({token_name}), Polygon地址: {polygon_address}, 交易记录: {tx_count}条")

        # 删除找到的条目
        for key in keys_to_delete:
            del zero_tx_data[key]

        deleted_count = len(keys_to_delete)

        if deleted_count > 0:
            # 保存更新后的数据
            with open(zero_tx_file, 'w', encoding='utf-8') as f:
                json.dump(zero_tx_data, f, indent=2, ensure_ascii=False)

            updated_count = len(zero_tx_data)
            logging.warning(f"已从zero_tx_stats_detailed.json中删除 {deleted_count} 条黑名单代币的相关数据")
            logging.info(f"数据统计: 原始 {original_count} 条 → 更新后 {updated_count} 条")

            # 详细记录删除的代币信息
            for detail in deleted_details:
                logging.info(f"  已删除: {detail['symbol']} ({detail['name']}) - {detail['eth_address']}")
        else:
            logging.info(f"在zero_tx_stats_detailed.json中未找到黑名单代币的相关数据")

    except Exception as e:
        logging.error(f"从zero_tx_stats_detailed.json删除黑名单数据时出错: {str(e)}")
        logging.error(traceback.format_exc())

def save_to_blacklist(eth_address: str, symbol: str) -> None:
    """
    将代币的eth_address保存到黑名单，并从zero_tx_stats_detailed.json中删除所有黑名单代币的数据

    Args:
        eth_address: 以太坊地址
        symbol: 代币符号
    """
    try:
        blacklist_dir = os.path.join(os.path.dirname(__file__), "results")
        os.makedirs(blacklist_dir, exist_ok=True)

        blacklist_file = os.path.join(blacklist_dir, "blacklist.json")

        # 读取现有黑名单
        blacklist_data = {}
        if os.path.exists(blacklist_file):
            with open(blacklist_file, 'r', encoding='utf-8') as f:
                blacklist_data = json.load(f)

        # 确保有eth_address数组
        if "eth_address" not in blacklist_data:
            blacklist_data["eth_address"] = []

        # 检查是否已存在
        is_new_addition = eth_address not in blacklist_data["eth_address"]

        if is_new_addition:
            blacklist_data["eth_address"].append(eth_address)

            # 添加详细信息（可选）
            if "details" not in blacklist_data:
                blacklist_data["details"] = []

            detail_entry = {
                "symbol": symbol,
                "eth_address": eth_address,
                "added_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "reason": "累计利润低于-10 USDT"
            }
            blacklist_data["details"].append(detail_entry)

            with open(blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(blacklist_data, f, indent=4, ensure_ascii=False)

            logging.warning(f"代币 {symbol} ({eth_address}) 已添加到黑名单")
        else:
            logging.info(f"代币 {symbol} ({eth_address}) 已在黑名单中")

        # 无论是否为新增，都清理所有黑名单代币的数据
        all_blacklist_addresses = blacklist_data.get("eth_address", [])
        if all_blacklist_addresses:
            logging.info(f"开始清理zero_tx_stats_detailed.json中所有黑名单代币的数据，共 {len(all_blacklist_addresses)} 个地址")
            remove_all_blacklisted_from_zero_tx_stats(all_blacklist_addresses)

    except Exception as e:
        logging.error(f"保存黑名单时出错: {str(e)}")
        logging.error(traceback.format_exc())

def save_executed_trade(record: dict, symbol: str) -> None:
    """
    保存已执行的交易记录

    Args:
        record: 交易记录
        symbol: 代币符号
    """
    try:
        # 创建交易记录目录
        trade_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
        os.makedirs(trade_dir, exist_ok=True)

        # 使用统一的文件名格式
        filename = f"{symbol}_new.json"
        filepath = os.path.join(trade_dir, filename)

        # 加载代币地址信息
        token_addresses = load_token_addresses(symbol)

        # 确保记录中包含完整的地址信息
        if not record.get('eth_address') and token_addresses['eth_address']:
            record['eth_address'] = token_addresses['eth_address']
        if not record.get('polygon_address') and token_addresses['polygon_address']:
            record['polygon_address'] = token_addresses['polygon_address']

        # 读取现有记录
        existing_records = []
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                existing_records = json.load(f)
                if not isinstance(existing_records, list):
                    existing_records = []

        # 添加新记录
        existing_records.append(record)

        # 只保留最新的5条记录
        if len(existing_records) > 5:
            existing_records = existing_records[-5:]

        # 计算累计利润
        total_profit = 0
        valid_records = 0

        for rec in existing_records:
            try:
                usdt_received = rec.get('usdt_received')
                usdt_input = rec.get('usdt_input')

                if usdt_received and usdt_input:
                    # 转换为浮点数进行计算
                    received = float(usdt_received) if isinstance(usdt_received, str) else usdt_received
                    input_amount = float(usdt_input) if isinstance(usdt_input, str) else usdt_input

                    profit = received - input_amount
                    total_profit += profit
                    valid_records += 1

            except (ValueError, TypeError) as e:
                # 跳过无效的记录
                logging.debug(f"跳过无效记录的利润计算: {str(e)}")
                continue

        # 在记录中添加累计利润信息
        for rec in existing_records:
            rec['cumulative_profit'] = total_profit
            rec['valid_profit_records'] = valid_records

        # 保存更新后的记录
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(existing_records, f, indent=4, ensure_ascii=False)

        logging.info(f"交易记录已保存: {filepath}")
        logging.info(f"累计利润: {total_profit:.4f} USDT (基于 {valid_records} 条有效记录)")

        # 检查是否需要加入黑名单
        if total_profit < -10 and valid_records > 0:
            eth_address = record.get('eth_address') or token_addresses['eth_address']
            if eth_address:
                save_to_blacklist(eth_address, symbol)

    except Exception as e:
        logging.error(f"保存交易记录时出错: {str(e)}")
        logging.error(traceback.format_exc())

def execute_trade_in_thread(opportunity: Dict) -> None:
    """
    在新线程中执行交易，不阻塞主线程
    
    Args:
        opportunity: 套利机会信息，包含以下字段：
            - symbol: 代币符号
            - polygon_address: Polygon链上的代币地址
            - eth_address: 以太坊链上的代币地址
            - bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)
            - optimal_usdt: USDT数量
            - token_amount: 代币数量
    """
    try:
        # 从套利机会信息创建执行器实例
        executor = BridgeArbExecutor.from_opportunity(opportunity)
        
        # 创建结果队列
        result_queue = queue.Queue()
        
        # 创建并启动新线程
        thread = threading.Thread(
            target=executor.execute_in_thread,
            args=(result_queue,),
            name=f"Trade_{executor.symbol}"
        )
        thread.daemon = False  # 设置为非守护线程，这样主程序退出后线程仍会继续运行
        thread.start()
        
        # 不等待线程完成，直接返回
        # 线程会在后台继续执行交易
        logging.info(f"交易 {executor.symbol} 已在后台启动，继续执行下一轮任务")
            
    except Exception as e:
        error_msg = f"启动交易线程时出错: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        # 不抛出异常，让主程序继续运行
        logging.error("继续执行下一轮任务")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="执行跨链套利交易")
    parser.add_argument("--symbol", required=True, help="代币符号")
    parser.add_argument("--chain", required=True, choices=['ethereum', 'polygon'], help="链名称")
    parser.add_argument("--amount", type=float, required=True, help="USDT数量")
    parser.add_argument("--token-address", required=True, help="代币地址")
    parser.add_argument("--bridge-direction", choices=['ethereum_to_polygon', 'polygon_to_ethereum'], help="交易方向")
    args = parser.parse_args()
    
    try:
        # 创建执行器实例
        executor = BridgeArbExecutor(
            symbol=args.symbol,
            chain=args.chain,
            token_address=args.token_address,
            amount=args.amount,
            bridge_direction=args.bridge_direction
        )
        
        # 创建并启动执行线程
        thread = threading.Thread(
            target=executor.execute_in_thread,
            name=f"Trade_{args.symbol}"
        )
        thread.start()
        
        # 等待线程完成
        thread.join()
        
        # 获取结果
        try:
            result = executor.result_queue.get(timeout=120)  # 等待最多2分钟
            if result.get("success"):
                executor.logger.info("交易执行成功")
                executor.logger.info(f"输出数量: {result['amount_out']} {args.symbol}")
            else:
                executor.logger.error(f"交易执行失败: {result.get('error', '未知错误')}")
        except queue.Empty:
            executor.logger.error("等待交易结果超时")
            
    except Exception as e:
        logging.error(f"执行交易时出错: {str(e)}")
        logging.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
