2025-07-28 04:29:52,222 - trade_BYN - INFO - ================================================================================
2025-07-28 04:29:52,222 - trade_BYN - INFO - 开始执行 BYN 买入交易 - 时间: 2025-07-28 04:29:52
2025-07-28 04:29:52,222 - trade_BYN - INFO - 链: polygon, 投入金额: 8.0 USDT
2025-07-28 04:29:52,222 - trade_BYN - INFO - 代币地址: 0x11602a402281974a70c2b4824d58ebede967e2be
2025-07-28 04:29:52,229 - trade_BYN - INFO - BYN: 将在polygon链上执行买入，代币地址: 0x11602a402281974a70c2b4824d58ebede967e2be
2025-07-28 04:29:52,229 - trade_BYN - INFO - BYN: 准备使用KyberSwap在polygon上执行8.0USDT买入BYN交易
2025-07-28 04:29:52,229 - trade_BYN - INFO - 执行买入交易，预期代币输出: 7000.496954015412
2025-07-28 04:30:00,970 - trade_BYN - INFO - BYN: 使用tx_token_change_tracker获取交易 0x0dd2328a68a97e1cf9896b95a7a3651232498563f85a245764e87a4cdf17c0bd 的代币数量...
2025-07-28 04:30:05,114 - trade_BYN - INFO - BYN: 从tx_token_change_tracker成功获取到代币数量: 6999.438605351441705993
2025-07-28 04:30:05,114 - trade_BYN - INFO - 交易执行成功:
2025-07-28 04:30:05,114 - trade_BYN - INFO -   交易哈希: 0x0dd2328a68a97e1cf9896b95a7a3651232498563f85a245764e87a4cdf17c0bd
2025-07-28 04:30:05,114 - trade_BYN - INFO -   实际输出数量: 6999.438605351442 BYN
2025-07-28 04:30:05,114 - trade_BYN - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-07-28 04:30:05,114 - trade_BYN - INFO - ================================================================================
2025-07-28 04:30:05,114 - trade_BYN - INFO - 开始执行 BYN 桥接操作 - 时间: 2025-07-28 04:30:05
2025-07-28 04:30:05,114 - trade_BYN - INFO - 桥接方向: polygon_to_ethereum
2025-07-28 04:30:05,114 - trade_BYN - INFO - 代币数量: 6999.438605351441705993 BYN
2025-07-28 04:30:05,114 - trade_BYN - INFO - 尝试初始化桥接 (第 1 次)
2025-07-28 04:30:07,006 - trade_BYN - INFO - 桥接初始化成功
2025-07-28 04:30:07,009 - trade_BYN - INFO - 从Polygon桥接到以太坊: 6999.438605351441705993 BYN
2025-07-28 05:58:37,353 - trade_BYN - INFO - 桥接操作成功完成
2025-07-28 05:58:37,354 - trade_BYN - INFO - Burn交易哈希: 0x04d5717766fa294b07818b73c91e99c8c581c24f83bcb678b990ad35712251d6
2025-07-28 05:58:37,354 - trade_BYN - INFO - Claim交易哈希: 0x8219efa975f221667f9b2bb822c33201604ab53754913126be25f4fc5c86ef13
2025-07-28 05:58:37,354 - trade_BYN - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\BYN_20250728_055837_bridge.json
2025-07-28 05:58:37,355 - trade_BYN - INFO - 桥接操作成功完成
2025-07-28 05:58:37,355 - trade_BYN - INFO - 开始执行卖出操作...
2025-07-28 05:58:37,355 - trade_BYN - INFO - ================================================================================
2025-07-28 05:58:37,355 - trade_BYN - INFO - 开始执行 BYN 卖出交易 - 时间: 2025-07-28 05:58:37
2025-07-28 05:58:37,360 - trade_BYN - INFO - 目标链: ethereum
2025-07-28 05:58:37,360 - trade_BYN - INFO - 目标链代币地址: ******************************************
2025-07-28 05:58:37,360 - trade_BYN - INFO - 在ethereum链上执行卖出操作
2025-07-28 05:58:37,360 - trade_BYN - INFO - 代币地址: ******************************************
2025-07-28 05:58:37,360 - trade_BYN - INFO - 卖出数量: 6999.438605351441705993 BYN
2025-07-28 05:58:37,361 - trade_BYN - INFO - 预期USDT输出: 10.111065 USDT
2025-07-28 05:58:40,118 - trade_BYN - INFO - 执行卖出交易，预期USDT输出: 10.111065
2025-07-28 05:59:11,998 - trade_BYN - INFO - 使用tx_token_change_tracker获取交易 0xf9f2c580c435bc9d1ebceb9ce6b00d162f8ef12ed9ed80f5e743a9426993e554 的USDT数量...
2025-07-28 05:59:15,546 - trade_BYN - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-28 05:59:15,547 - trade_BYN - INFO - 成功获取到USDT数量: 10.130564
2025-07-28 05:59:15,547 - trade_BYN - INFO - 卖出交易执行成功:
2025-07-28 05:59:15,547 - trade_BYN - INFO -   交易哈希: 0xf9f2c580c435bc9d1ebceb9ce6b00d162f8ef12ed9ed80f5e743a9426993e554
2025-07-28 05:59:15,548 - trade_BYN - INFO -   实际收到USDT数量: 10.130564
2025-07-28 05:59:15,548 - trade_BYN - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\BYN_20250728_055915_sell.json
2025-07-28 05:59:15,548 - trade_BYN - INFO - 卖出操作成功完成
2025-07-28 05:59:15,549 - trade_BYN - INFO - 交易执行完成，耗时: 5363.33秒
2025-07-28 05:59:15,549 - trade_BYN - INFO - ================================================================================
