2025-07-28 05:55:58,869 - trade_MIMO - INFO - ================================================================================
2025-07-28 05:55:58,870 - trade_MIMO - INFO - 开始执行 MIMO 买入交易 - 时间: 2025-07-28 05:55:58
2025-07-28 05:55:58,870 - trade_MIMO - INFO - 链: ethereum, 投入金额: 11.56 USDT
2025-07-28 05:55:58,870 - trade_MIMO - INFO - 代币地址: ******************************************
2025-07-28 05:55:58,875 - trade_MIMO - INFO - MIMO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-28 05:55:58,875 - trade_MIMO - INFO - MIMO: 准备使用KyberSwap在ethereum上执行11.56USDT买入MIMO交易
2025-07-28 05:55:58,875 - trade_MIMO - INFO - 执行买入交易，预期代币输出: 3916.4070692247246
2025-07-28 05:56:12,266 - trade_MIMO - INFO - MIMO: 使用tx_token_change_tracker获取交易 0xc1985796ec60713b9846b25bdf04050d98a5c263749cc7510269ad9bada5a443 的代币数量...
2025-07-28 05:56:15,515 - trade_MIMO - INFO - MIMO: 从tx_token_change_tracker成功获取到代币数量: 3914.68130160718198201
2025-07-28 05:56:15,515 - trade_MIMO - INFO - 交易执行成功:
2025-07-28 05:56:15,515 - trade_MIMO - INFO -   交易哈希: 0xc1985796ec60713b9846b25bdf04050d98a5c263749cc7510269ad9bada5a443
2025-07-28 05:56:15,515 - trade_MIMO - INFO -   实际输出数量: 3914.681301607182 MIMO
2025-07-28 05:56:15,515 - trade_MIMO - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-28 05:56:15,516 - trade_MIMO - INFO - ================================================================================
2025-07-28 05:56:15,516 - trade_MIMO - INFO - 开始执行 MIMO 桥接操作 - 时间: 2025-07-28 05:56:15
2025-07-28 05:56:15,516 - trade_MIMO - INFO - 桥接方向: ethereum_to_polygon
2025-07-28 05:56:15,516 - trade_MIMO - INFO - 代币数量: 3914.68130160718198201 MIMO
2025-07-28 05:56:15,516 - trade_MIMO - INFO - 尝试初始化桥接 (第 1 次)
2025-07-28 05:56:16,472 - trade_MIMO - INFO - 桥接初始化成功
2025-07-28 05:56:16,473 - trade_MIMO - INFO - 从以太坊桥接到Polygon: 3914.68130160718198201 MIMO
2025-07-28 06:18:54,358 - trade_MIMO - INFO - 桥接操作成功完成
2025-07-28 06:18:54,359 - trade_MIMO - INFO - 桥接交易哈希: 0x740bd8201b0bf99a223b0fbafd7745e01dd3bb85af9f4663483066315bf1f849
2025-07-28 06:18:54,360 - trade_MIMO - INFO - Polygon交易哈希: None
2025-07-28 06:18:54,362 - trade_MIMO - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\MIMO_20250728_061854_bridge.json
2025-07-28 06:18:54,363 - trade_MIMO - INFO - 桥接操作成功完成
2025-07-28 06:18:54,364 - trade_MIMO - INFO - 开始执行卖出操作...
2025-07-28 06:18:54,365 - trade_MIMO - INFO - ================================================================================
2025-07-28 06:18:54,365 - trade_MIMO - INFO - 开始执行 MIMO 卖出交易 - 时间: 2025-07-28 06:18:54
2025-07-28 06:18:54,374 - trade_MIMO - INFO - 目标链: polygon
2025-07-28 06:18:54,375 - trade_MIMO - INFO - 目标链代币地址: 0xadac33f543267c4d59a8c299cf804c303bc3e4ac
2025-07-28 06:18:54,375 - trade_MIMO - INFO - 在polygon链上执行卖出操作
2025-07-28 06:18:54,375 - trade_MIMO - INFO - 代币地址: 0xadac33f543267c4d59a8c299cf804c303bc3e4ac
2025-07-28 06:18:54,375 - trade_MIMO - INFO - 卖出数量: 3914.68130160718198201 MIMO
2025-07-28 06:18:54,375 - trade_MIMO - INFO - 预期USDT输出: 14.356137 USDT
2025-07-28 06:18:58,691 - trade_MIMO - INFO - 执行卖出交易，预期USDT输出: 14.356137
2025-07-28 06:19:14,375 - trade_MIMO - INFO - 使用tx_token_change_tracker获取交易 0x8393f0caa966acf0d5a6151122ab091acbe76a7fd71e47071773f3bdec8d17a5 的USDT数量...
2025-07-28 06:19:26,537 - trade_MIMO - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-28 06:19:26,537 - trade_MIMO - INFO - 成功获取到USDT数量: 14.394245
2025-07-28 06:19:26,538 - trade_MIMO - INFO - 卖出交易执行成功:
2025-07-28 06:19:26,538 - trade_MIMO - INFO -   交易哈希: 0x8393f0caa966acf0d5a6151122ab091acbe76a7fd71e47071773f3bdec8d17a5
2025-07-28 06:19:26,538 - trade_MIMO - INFO -   实际收到USDT数量: 14.394245
2025-07-28 06:19:26,539 - trade_MIMO - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\MIMO_20250728_061926_sell.json
2025-07-28 06:19:26,539 - trade_MIMO - INFO - 卖出操作成功完成
2025-07-28 06:19:26,539 - trade_MIMO - INFO - 交易执行完成，耗时: 1407.67秒
2025-07-28 06:19:26,539 - trade_MIMO - INFO - ================================================================================
