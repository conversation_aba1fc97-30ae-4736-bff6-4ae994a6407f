2025-07-26 21:38:45,260 - trade_WINTER - INFO - ================================================================================
2025-07-26 21:38:45,261 - trade_WINTER - INFO - 开始执行 WINTER 买入交易 - 时间: 2025-07-26 21:38:45
2025-07-26 21:38:45,261 - trade_WINTER - INFO - 链: ethereum, 投入金额: 68.28 USDT
2025-07-26 21:38:45,261 - trade_WINTER - INFO - 代币地址: ******************************************
2025-07-26 21:38:45,272 - trade_WINTER - INFO - WINTER: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-26 21:38:45,273 - trade_WINTER - INFO - WINTER: 准备使用KyberSwap在ethereum上执行68.28USDT买入WINTER交易
2025-07-26 21:38:45,273 - trade_WINTER - INFO - 执行买入交易，预期代币输出: 14527.59970112128
2025-07-26 21:39:23,802 - trade_WINTER - INFO - WINTER: 使用tx_token_change_tracker获取交易 0xb3cff11a0f02507f59124fcbf881abc6115b2ab516f5e220c01eb774da09fb67 的代币数量...
2025-07-26 21:39:26,750 - trade_WINTER - INFO - WINTER: 从tx_token_change_tracker成功获取到代币数量: 14506.221134521895117004
2025-07-26 21:39:26,751 - trade_WINTER - INFO - 交易执行成功:
2025-07-26 21:39:26,751 - trade_WINTER - INFO -   交易哈希: 0xb3cff11a0f02507f59124fcbf881abc6115b2ab516f5e220c01eb774da09fb67
2025-07-26 21:39:26,751 - trade_WINTER - INFO -   实际输出数量: 14506.221134521895 WINTER
2025-07-26 21:39:26,751 - trade_WINTER - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-26 21:39:26,751 - trade_WINTER - INFO - ================================================================================
2025-07-26 21:39:26,751 - trade_WINTER - INFO - 开始执行 WINTER 桥接操作 - 时间: 2025-07-26 21:39:26
2025-07-26 21:39:26,751 - trade_WINTER - INFO - 桥接方向: ethereum_to_polygon
2025-07-26 21:39:26,751 - trade_WINTER - INFO - 代币数量: 14506.221134521895117004 WINTER
2025-07-26 21:39:29,018 - trade_WINTER - INFO - 从以太坊桥接到Polygon: 14506.221134521895117004 WINTER
2025-07-26 21:58:28,057 - trade_WINTER - INFO - 桥接操作成功完成
2025-07-26 21:58:28,057 - trade_WINTER - INFO - 桥接交易哈希: 0xd98fcea96b4a192549f3829bd4e7c81539a1ceb9ea6ad3d00161ce0f9fbd11d0
2025-07-26 21:58:28,057 - trade_WINTER - INFO - Polygon交易哈希: None
2025-07-26 21:58:28,058 - trade_WINTER - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\WINTER_20250726_215828_bridge.json
2025-07-26 21:58:28,058 - trade_WINTER - INFO - 桥接操作成功完成
2025-07-26 21:58:28,059 - trade_WINTER - INFO - 开始执行卖出操作...
2025-07-26 21:58:28,059 - trade_WINTER - INFO - ================================================================================
2025-07-26 21:58:28,059 - trade_WINTER - INFO - 开始执行 WINTER 卖出交易 - 时间: 2025-07-26 21:58:28
2025-07-26 21:58:28,065 - trade_WINTER - INFO - 目标链: polygon
2025-07-26 21:58:28,066 - trade_WINTER - INFO - 目标链代币地址: 0x51540d15957bdc0fdb87d32616c8d658d59f77c6
2025-07-26 21:58:28,066 - trade_WINTER - INFO - 在polygon链上执行卖出操作
2025-07-26 21:58:28,066 - trade_WINTER - INFO - 代币地址: 0x51540d15957bdc0fdb87d32616c8d658d59f77c6
2025-07-26 21:58:28,066 - trade_WINTER - INFO - 卖出数量: 14506.221134521895117004 WINTER
2025-07-26 21:58:28,066 - trade_WINTER - INFO - 预期USDT输出: 70.170629 USDT
2025-07-26 21:58:29,692 - trade_WINTER - INFO - 执行卖出交易，预期USDT输出: 70.170629
2025-07-26 21:59:02,027 - trade_WINTER - INFO - 使用tx_token_change_tracker获取交易 0x1c0fb89a8931cd660350287533d2490bc323e9adf912cfac7dd903c89ffabc27 的USDT数量...
2025-07-26 21:59:06,618 - trade_WINTER - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-26 21:59:06,618 - trade_WINTER - INFO - 成功获取到USDT数量: 70.150745
2025-07-26 21:59:06,618 - trade_WINTER - INFO - 卖出交易执行成功:
2025-07-26 21:59:06,618 - trade_WINTER - INFO -   交易哈希: 0x1c0fb89a8931cd660350287533d2490bc323e9adf912cfac7dd903c89ffabc27
2025-07-26 21:59:06,618 - trade_WINTER - INFO -   实际收到USDT数量: 70.150745
2025-07-26 21:59:06,620 - trade_WINTER - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\WINTER_20250726_215906_sell.json
2025-07-26 21:59:06,620 - trade_WINTER - INFO - 卖出操作成功完成
2025-07-26 21:59:06,620 - trade_WINTER - INFO - 交易执行完成，耗时: 1221.36秒
2025-07-26 21:59:06,621 - trade_WINTER - INFO - ================================================================================
