2025-07-26 08:37:41,468 - trade_renDOGE - INFO - ================================================================================
2025-07-26 08:37:41,469 - trade_renDOGE - INFO - 开始执行 renDOGE 买入交易 - 时间: 2025-07-26 08:37:41
2025-07-26 08:37:41,471 - trade_renDOGE - INFO - 链: ethereum, 投入金额: 16.0 USDT
2025-07-26 08:37:41,472 - trade_renDOGE - INFO - 代币地址: ******************************************
2025-07-26 08:37:41,483 - trade_renDOGE - INFO - renDOGE: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-26 08:37:41,484 - trade_renDOGE - INFO - renDOGE: 准备使用KyberSwap在ethereum上执行16.0USDT买入renDOGE交易
2025-07-26 08:37:41,484 - trade_renDOGE - INFO - 执行买入交易，预期代币输出: 7.5560843399e-08
2025-07-26 08:38:12,656 - trade_renDOGE - INFO - renDOGE: 使用tx_token_change_tracker获取交易 0x2998f8fd7c47d3394be4f4c2bfded90d26f3ca80756bced974e8786fbee3768d 的代币数量...
2025-07-26 08:38:15,274 - trade_renDOGE - INFO - renDOGE: 从tx_token_change_tracker成功获取到代币数量: 756.03055863
2025-07-26 08:38:15,274 - trade_renDOGE - INFO - 交易执行成功:
2025-07-26 08:38:15,274 - trade_renDOGE - INFO -   交易哈希: 0x2998f8fd7c47d3394be4f4c2bfded90d26f3ca80756bced974e8786fbee3768d
2025-07-26 08:38:15,274 - trade_renDOGE - INFO -   实际输出数量: 756.03055863 renDOGE
2025-07-26 08:38:15,275 - trade_renDOGE - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-26 08:38:15,275 - trade_renDOGE - INFO - ================================================================================
2025-07-26 08:38:15,275 - trade_renDOGE - INFO - 开始执行 renDOGE 桥接操作 - 时间: 2025-07-26 08:38:15
2025-07-26 08:38:15,275 - trade_renDOGE - INFO - 桥接方向: ethereum_to_polygon
2025-07-26 08:38:15,275 - trade_renDOGE - INFO - 代币数量: 756.03055863 renDOGE
2025-07-26 08:38:17,952 - trade_renDOGE - INFO - 从以太坊桥接到Polygon: 756.03055863 renDOGE
2025-07-26 08:58:23,157 - trade_renDOGE - INFO - 桥接操作成功完成
2025-07-26 08:58:23,159 - trade_renDOGE - INFO - 桥接交易哈希: 0xf5101d6f4044763f08c03452a01b8e827a415437fd3a48c2ec7c62ed0dc24868
2025-07-26 08:58:23,161 - trade_renDOGE - INFO - Polygon交易哈希: None
2025-07-26 08:58:23,162 - trade_renDOGE - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\renDOGE_20250726_085823_bridge.json
2025-07-26 08:58:23,162 - trade_renDOGE - INFO - 桥接操作成功完成
2025-07-26 08:58:23,162 - trade_renDOGE - INFO - 开始执行卖出操作...
2025-07-26 08:58:23,162 - trade_renDOGE - INFO - ================================================================================
2025-07-26 08:58:23,163 - trade_renDOGE - INFO - 开始执行 renDOGE 卖出交易 - 时间: 2025-07-26 08:58:23
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 目标链: polygon
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 目标链代币地址: 0x7c4a54f5d20b4023d6746f1f765f4dfe7c39a7e6
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 在polygon链上执行卖出操作
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 代币地址: 0x7c4a54f5d20b4023d6746f1f765f4dfe7c39a7e6
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 卖出数量: 756.03055863 renDOGE
2025-07-26 08:58:23,171 - trade_renDOGE - INFO - 预期USDT输出: 17.608467 USDT
2025-07-26 08:58:27,962 - trade_renDOGE - INFO - 执行卖出交易，预期USDT输出: 17.608467
2025-07-26 08:58:44,944 - trade_renDOGE - INFO - 使用tx_token_change_tracker获取交易 0xba1b0e808fc1c4c2255c3fd4935a628b17f55130b37e8ef1d3b1f9611a9915a7 的USDT数量...
2025-07-26 08:58:47,948 - trade_renDOGE - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-26 08:58:47,949 - trade_renDOGE - INFO - 成功获取到USDT数量: 17.598245
2025-07-26 08:58:47,949 - trade_renDOGE - INFO - 卖出交易执行成功:
2025-07-26 08:58:47,949 - trade_renDOGE - INFO -   交易哈希: 0xba1b0e808fc1c4c2255c3fd4935a628b17f55130b37e8ef1d3b1f9611a9915a7
2025-07-26 08:58:47,949 - trade_renDOGE - INFO -   实际收到USDT数量: 17.598245
2025-07-26 08:58:47,949 - trade_renDOGE - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\renDOGE_20250726_085847_sell.json
2025-07-26 08:58:47,949 - trade_renDOGE - INFO - 卖出操作成功完成
2025-07-26 08:58:47,950 - trade_renDOGE - INFO - 交易执行完成，耗时: 1266.48秒
2025-07-26 08:58:47,950 - trade_renDOGE - INFO - ================================================================================
