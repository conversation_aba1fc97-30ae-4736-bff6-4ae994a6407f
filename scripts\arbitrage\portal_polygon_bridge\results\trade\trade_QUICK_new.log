2025-07-29 00:46:53,210 - trade_QUICK - INFO - ================================================================================
2025-07-29 00:46:53,211 - trade_QUICK - INFO - 开始执行 QUICK 买入交易 - 时间: 2025-07-29 00:46:53
2025-07-29 00:46:53,211 - trade_QUICK - INFO - 链: ethereum, 投入金额: 168.98 USDT
2025-07-29 00:46:53,211 - trade_QUICK - INFO - 代币地址: ******************************************
2025-07-29 00:46:53,218 - trade_QUICK - INFO - QUICK: 将在ethereum链上执行买入，代币地址: ******************************************
2025-07-29 00:46:53,218 - trade_QUICK - INFO - QUICK: 准备使用KyberSwap在ethereum上执行168.98USDT买入QUICK交易
2025-07-29 00:46:53,219 - trade_QUICK - INFO - 执行买入交易，预期代币输出: 7.126197672919263
2025-07-29 00:47:21,831 - trade_QUICK - INFO - QUICK: 使用tx_token_change_tracker获取交易 0x68f24a96afd7004eb7fd57a1109b7db0fc77a5c7082e71ff8b66f23e680203d7 的代币数量...
2025-07-29 00:47:25,540 - trade_QUICK - INFO - QUICK: 从tx_token_change_tracker成功获取到代币数量: 7.126059044340826121
2025-07-29 00:47:25,540 - trade_QUICK - INFO - 交易执行成功:
2025-07-29 00:47:25,541 - trade_QUICK - INFO -   交易哈希: 0x68f24a96afd7004eb7fd57a1109b7db0fc77a5c7082e71ff8b66f23e680203d7
2025-07-29 00:47:25,541 - trade_QUICK - INFO -   实际输出数量: 7.126059044340826 QUICK
2025-07-29 00:47:25,541 - trade_QUICK - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-07-29 00:47:25,541 - trade_QUICK - INFO - ================================================================================
2025-07-29 00:47:25,541 - trade_QUICK - INFO - 开始执行 QUICK 桥接操作 - 时间: 2025-07-29 00:47:25
2025-07-29 00:47:25,541 - trade_QUICK - INFO - 桥接方向: ethereum_to_polygon
2025-07-29 00:47:25,541 - trade_QUICK - INFO - 代币数量: 7.126059044340826121 QUICK
2025-07-29 00:47:25,541 - trade_QUICK - INFO - 尝试初始化桥接 (第 1 次)
2025-07-29 00:47:28,085 - trade_QUICK - INFO - 桥接初始化成功
2025-07-29 00:47:28,085 - trade_QUICK - INFO - 从以太坊桥接到Polygon: 7.126059044340826121 QUICK
2025-07-29 01:04:38,256 - trade_QUICK - INFO - 桥接操作成功完成
2025-07-29 01:04:38,257 - trade_QUICK - INFO - 桥接交易哈希: 0xd147b9fecbcf37381657ec186fe81a507232c40c56c175886f0ee577c7a738c8
2025-07-29 01:04:38,257 - trade_QUICK - INFO - Polygon交易哈希: None
2025-07-29 01:04:38,258 - trade_QUICK - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\QUICK_20250729_010438_bridge.json
2025-07-29 01:04:38,258 - trade_QUICK - INFO - 桥接操作成功完成
2025-07-29 01:04:38,258 - trade_QUICK - INFO - 开始执行卖出操作...
2025-07-29 01:04:38,258 - trade_QUICK - INFO - ================================================================================
2025-07-29 01:04:38,258 - trade_QUICK - INFO - 开始执行 QUICK 卖出交易 - 时间: 2025-07-29 01:04:38
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 目标链: polygon
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 目标链代币地址: 0x831753dd7087cac61ab5644b308642cc1c33dc13
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 在polygon链上执行卖出操作
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 代币地址: 0x831753dd7087cac61ab5644b308642cc1c33dc13
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 卖出数量: 7.126059044340826121 QUICK
2025-07-29 01:04:38,265 - trade_QUICK - INFO - 预期USDT输出: 182.292861 USDT
2025-07-29 01:04:40,342 - trade_QUICK - INFO - 执行卖出交易，预期USDT输出: 182.292861
2025-07-29 01:05:13,639 - trade_QUICK - INFO - 使用tx_token_change_tracker获取交易 0x83d5a6bd06a5d42c6b1025f49c77221c58942c8f4bcf39844d71429e1a77c973 的USDT数量...
2025-07-29 01:05:16,038 - trade_QUICK - INFO - 从tx_token_change_tracker成功获取到USDT数量
2025-07-29 01:05:16,038 - trade_QUICK - INFO - 成功获取到USDT数量: 181.680756
2025-07-29 01:05:16,038 - trade_QUICK - INFO - 卖出交易执行成功:
2025-07-29 01:05:16,038 - trade_QUICK - INFO -   交易哈希: 0x83d5a6bd06a5d42c6b1025f49c77221c58942c8f4bcf39844d71429e1a77c973
2025-07-29 01:05:16,038 - trade_QUICK - INFO -   实际收到USDT数量: 181.680756
2025-07-29 01:05:16,039 - trade_QUICK - INFO - 卖出记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\sell\QUICK_20250729_010516_sell.json
2025-07-29 01:05:16,039 - trade_QUICK - INFO - 卖出操作成功完成
2025-07-29 01:05:16,039 - trade_QUICK - INFO - 交易执行完成，耗时: 1102.83秒
2025-07-29 01:05:16,039 - trade_QUICK - INFO - ================================================================================
